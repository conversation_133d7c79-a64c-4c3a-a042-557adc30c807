[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

[tool.poetry]
name = "trigger-service"
version = "0.1.0"
description = "Event-based trigger service for workflow automation"
authors = ["Trigger Service Team <<EMAIL>>"]
readme = "README.md"

[tool.poetry.dependencies]
python = ">=3.9,<3.13"
fastapi = "0.104.1"
uvicorn = {extras = ["standard"], version = "0.24.0"}
sqlalchemy = "^2.0.29"
alembic = "1.12.1"
psycopg2-binary = "^2.9.10"
google-api-python-client = "2.108.0"
google-auth = "2.23.4"
google-auth-oauthlib = "1.1.0"
pydantic = "^2.7.1"
pydantic-settings = "^2.2.1"
celery = "5.3.4"
redis = "5.0.1"
httpx = "0.25.2"
structlog = "23.2.0"
python-multipart = "0.0.6"
python-dateutil = "2.8.2"
asyncpg = "^0.30.0"
google-auth-httplib2 = "^0.2.0"
python-dotenv = "^1.1.0"
greenlet = "^3.2.3"
pytz = "^2024.1"
croniter = "^1.4.1"

[tool.poetry.group.dev.dependencies]
pytest = "7.4.3"
pytest-asyncio = "0.21.1"
pytest-cov = "4.1.0"
pytest-mock = "3.12.0"
black = "23.11.0"
isort = "5.12.0"
flake8 = "6.1.0"
mypy = "1.7.1"
types-redis = "********"
types-python-dateutil = "*********"
pre-commit = "3.5.0"
ipython = "8.17.2"
ipdb = "0.13.13"
locust = "2.17.0"
mkdocs = "1.5.3"
mkdocs-material = "9.4.8"

[tool.poetry.scripts]
trigger-service = "src.main:main"

