# Application Configuration
DEBUG=true
HOST=0.0.0.0
PORT=8000
LOG_LEVEL=INFO
LOG_FORMAT=json

# CORS Configuration
CORS_ORIGINS=["http://localhost:3000","http://localhost:8080","http://127.0.0.1:3000"]

# Database Configuration
DATABASE_URL=postgresql://trigger_user:trigger_password@localhost:5432/trigger_db

# Auth Service Configuration
AUTH_SERVICE_URL=https://auth-service.example.com
AUTH_SERVICE_API_KEY=your-auth-service-api-key

# Workflow Service Configuration
WORKFLOW_SERVICE_URL=https://ruh-test-api.rapidinnovation.dev
WORKFLOW_SERVICE_API_KEY=your-workflow-service-api-key

# Google Calendar Configuration
GOOGLE_CALENDAR_WEBHOOK_URL=https://trigger-service.example.com/api/v1/webhooks/google-calendar

# Google Drive Configuration
GOOGLE_DRIVE_WEBHOOK_URL=https://trigger-service.example.com/webhooks/google-drive-service-account
GOOGLE_DRIVE_SERVICE_ACCOUNT_CREDENTIALS_PATH=service-account-credentials.json

# Google Drive Service Account Credentials (Alternative to JSON file)
# You can either use the JSON file path above OR set these environment variables
GOOGLE_DRIVE_SERVICE_ACCOUNT_TYPE=service_account
GOOGLE_DRIVE_SERVICE_ACCOUNT_PROJECT_ID=your-project-id
GOOGLE_DRIVE_SERVICE_ACCOUNT_PRIVATE_KEY_ID=your-private-key-id
GOOGLE_DRIVE_SERVICE_ACCOUNT_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\nYour private key here\n-----END PRIVATE KEY-----\n"
GOOGLE_DRIVE_SERVICE_ACCOUNT_CLIENT_EMAIL=<EMAIL>
GOOGLE_DRIVE_SERVICE_ACCOUNT_CLIENT_ID=your-client-id
GOOGLE_DRIVE_SERVICE_ACCOUNT_AUTH_URI=https://accounts.google.com/o/oauth2/auth
GOOGLE_DRIVE_SERVICE_ACCOUNT_TOKEN_URI=https://oauth2.googleapis.com/token
GOOGLE_DRIVE_SERVICE_ACCOUNT_AUTH_PROVIDER_X509_CERT_URL=https://www.googleapis.com/oauth2/v1/certs
GOOGLE_DRIVE_SERVICE_ACCOUNT_CLIENT_X509_CERT_URL=https://www.googleapis.com/robot/v1/metadata/x509/your-service-account%40your-project.iam.gserviceaccount.com
GOOGLE_DRIVE_SERVICE_ACCOUNT_UNIVERSE_DOMAIN=googleapis.com

# Redis Configuration (for Celery)
REDIS_URL=redis://localhost:6379/0

# Celery Configuration
CELERY_BROKER_URL=redis://localhost:6379/0
CELERY_RESULT_BACKEND=redis://localhost:6379/0

# Security
SECRET_KEY=your-secret-key-here
API_KEY=your-api-key-for-internal-services

# External Service Timeouts (seconds)
HTTP_TIMEOUT=30
AUTH_SERVICE_TIMEOUT=10
WORKFLOW_SERVICE_TIMEOUT=60

# Retry Configuration
MAX_RETRY_ATTEMPTS=5
RETRY_BACKOFF_FACTOR=2
RETRY_MAX_DELAY=300

# Rate Limiting
RATE_LIMIT_REQUESTS_PER_MINUTE=100
RATE_LIMIT_BURST=20

# Monitoring
ENABLE_METRICS=true
METRICS_PORT=9090
