apiVersion: v1
kind: ServiceAccount
metadata:
  name: trigger-service-ai-sa
  namespace: ruh-catalyst
  labels:
    name: trigger-service-ai-sa
    namespace: ruh-catalyst
    app: trigger-service-ai
    deployment: trigger-service-ai-dp
---
# Create Deployment
apiVersion: apps/v1
kind: Deployment
metadata:
  name: trigger-service-ai-dp
  namespace: ruh-catalyst
  labels:
    name: trigger-service-ai-dp
    namespace: ruh-catalyst
    app: trigger-service-ai
    serviceaccount: trigger-service-ai-sa
    deployment: trigger-service-ai-dp
spec:
  replicas: 1
  selector:
    matchLabels:
      app: trigger-service-ai
      deployment: trigger-service-ai-dp
  template:
    metadata:
      labels:
        namespace: ruh-catalyst
        app: trigger-service-ai
        deployment: trigger-service-ai-dp
    spec:
      serviceAccountName: trigger-service-ai-sa      
      containers:
      - name: trigger-service-ai
        image: us-central1-docker.pkg.dev/<PROJECT_ID>/<REPOSITORY>/<IMAGE_NAME>:<ENV>-<VERSION>
        resources:
          requests:
            memory: 64Mi
            cpu: 50m
          limits:
            memory: 1024Mi
            cpu: 250m
        ports:
        - containerPort: 50059
      #   readinessProbe:
      #     tcpSocket:
      #       port: 5001
      #     initialDelaySeconds: 5
      #     periodSeconds: 10
      #   livenessProbe:
      #     tcpSocket:
      #       port: 5001
      #     initialDelaySeconds: 15
      #     periodSeconds: 20
      # tolerations:
      # - key: "spotInstance"
      #   operator: "Equal"
      #   value: "true"
      #   effect: "PreferNoSchedule"
      # nodeSelector:    
      #   eks.amazonaws.com/capacityType: SPOT       
---
#### Create Service
apiVersion: v1
kind: Service
metadata:
  name: trigger-service-ai-svc
  namespace: ruh-catalyst
spec:
  selector:
    app: trigger-service-ai
    deployment: trigger-service-ai-dp
  ports:
    - protocol: TCP
      port: 80
      targetPort: 50059
  sessionAffinityConfig:
    clientIP:
      timeoutSeconds: 100000
---
### Create HPA
# apiVersion: autoscaling/v2beta1
# kind: HorizontalPodAutoscaler
# metadata:
#   name:trigger-service-user-hpa
#   namespace: ruh-catalyst
# spec:
#   scaleTargetRef:
#     apiVersion: apps/v1
#     kind: Deployment
#     name:trigger-service-user-dp
#   minReplicas: 1
#   maxReplicas: 2
#   metrics:
#     - type: Resource
#       resource:
#         name: cpu
#         targetAverageUtilization: 60
---
### Create Nginx Ingress
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: trigger-service-user-ingress
  namespace: ruh-catalyst
spec:
  ingressClassName: nginx
  rules:
  - host: trigger-api-ruh-catalyst.rapidinnovation.dev
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: trigger-service-ai-svc
            port:
              number: 80
