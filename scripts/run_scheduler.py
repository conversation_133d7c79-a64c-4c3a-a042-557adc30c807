#!/usr/bin/env python3
"""
Production Scheduler Service Runner

This script provides a command-line interface for running the scalable scheduler service
with proper signal handling, logging, and monitoring capabilities.

Usage:
    python scripts/run_scheduler.py [options]

Options:
    --redis-url         Redis connection URL (default: redis://localhost:6379)
    --max-schedulers    Maximum concurrent schedulers (default: 10)
    --batch-size        Scheduler batch processing size (default: 50)
    --worker-concurrency Task worker concurrency (default: 5)
    --log-level         Logging level (default: INFO)
    --metrics-port      Port for metrics endpoint (default: 8080)
    --health-port       Port for health check endpoint (default: 8081)
    --daemon            Run as daemon process
    --pid-file          PID file path for daemon mode
"""

import asyncio
import argparse
import signal
import sys
import os
import logging
from pathlib import Path
from typing import Optional

# Add project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.core.scheduler_service import SchedulerService, get_scheduler_service
from src.utils.logger import get_logger, setup_logging
from src.utils.config import get_settings

logger = get_logger(__name__)


class SchedulerServiceRunner:
    """Runner for the scheduler service with proper lifecycle management."""

    def __init__(self, args):
        self.args = args
        self.service: Optional[SchedulerService] = None
        self.running = False
        self.shutdown_event = asyncio.Event()

        # Setup signal handlers
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)

        logger.info("SchedulerServiceRunner initialized")

    def _signal_handler(self, signum, frame):
        """Handle shutdown signals."""
        logger.info(f"Received signal {signum}, initiating graceful shutdown...")
        self.shutdown_event.set()

    async def start_metrics_server(self):
        """Start a simple HTTP server for metrics and health checks."""
        from aiohttp import web

        async def metrics_handler(request):
            """Handle metrics requests."""
            try:
                if self.service:
                    metrics = await self.service.get_metrics()
                    return web.json_response(metrics)
                else:
                    return web.json_response(
                        {"error": "Service not initialized"}, status=503
                    )
            except Exception as e:
                logger.error(f"Error getting metrics: {e}", exc_info=True)
                return web.json_response({"error": str(e)}, status=500)

        async def health_handler(request):
            """Handle health check requests."""
            try:
                if self.service:
                    health = await self.service.health_check()
                    status_code = 200 if health["status"] == "healthy" else 503
                    return web.json_response(health, status=status_code)
                else:
                    return web.json_response(
                        {"status": "unhealthy", "error": "Service not initialized"},
                        status=503,
                    )
            except Exception as e:
                logger.error(f"Error performing health check: {e}", exc_info=True)
                return web.json_response(
                    {"status": "unhealthy", "error": str(e)}, status=500
                )

        # Create metrics app
        metrics_app = web.Application()
        metrics_app.router.add_get("/metrics", metrics_handler)

        # Create health app
        health_app = web.Application()
        health_app.router.add_get("/health", health_handler)
        health_app.router.add_get("/ready", health_handler)

        # Start servers
        metrics_runner = web.AppRunner(metrics_app)
        await metrics_runner.setup()
        metrics_site = web.TCPSite(metrics_runner, "localhost", self.args.metrics_port)
        await metrics_site.start()

        health_runner = web.AppRunner(health_app)
        await health_runner.setup()
        health_site = web.TCPSite(health_runner, "localhost", self.args.health_port)
        await health_site.start()

        logger.info(f"Metrics server started on port {self.args.metrics_port}")
        logger.info(f"Health check server started on port {self.args.health_port}")

        return metrics_runner, health_runner

    async def run_scheduler_loop(self):
        """Main scheduler processing loop."""
        logger.info("Starting scheduler processing loop...")

        while self.running and not self.shutdown_event.is_set():
            try:
                # Run scheduler cycle
                await self.service.run_scheduler_cycle()

                # Wait before next cycle (configurable interval)
                cycle_interval = getattr(
                    self.args, "cycle_interval", 30
                )  # 30 seconds default
                await asyncio.wait_for(
                    self.shutdown_event.wait(), timeout=cycle_interval
                )

            except asyncio.TimeoutError:
                # Normal timeout, continue to next cycle
                continue
            except Exception as e:
                logger.error(f"Error in scheduler loop: {e}", exc_info=True)
                # Wait a bit before retrying to avoid tight error loops
                await asyncio.sleep(5)

    async def run(self):
        """Main run method."""
        logger.info("Starting SchedulerServiceRunner...")

        try:
            # Initialize service
            self.service = SchedulerService(
                redis_url=self.args.redis_url,
                max_concurrent_schedulers=self.args.max_schedulers,
                batch_size=self.args.batch_size,
                worker_concurrency=self.args.worker_concurrency,
            )

            await self.service.initialize()
            logger.info("Scheduler service initialized")

            # Start metrics and health servers
            metrics_runner, health_runner = await self.start_metrics_server()

            # Start the service
            await self.service.start()
            self.running = True

            logger.info("Scheduler service started successfully")
            logger.info(
                f"Metrics available at: http://localhost:{self.args.metrics_port}/metrics"
            )
            logger.info(
                f"Health checks available at: http://localhost:{self.args.health_port}/health"
            )

            # Run scheduler loop
            await self.run_scheduler_loop()

        except Exception as e:
            logger.error(f"Fatal error in scheduler service: {e}", exc_info=True)
            raise
        finally:
            # Cleanup
            logger.info("Shutting down scheduler service...")
            self.running = False

            if self.service:
                await self.service.stop()

            # Cleanup HTTP servers
            try:
                await metrics_runner.cleanup()
                await health_runner.cleanup()
            except:
                pass

            logger.info("Scheduler service shutdown complete")


def write_pid_file(pid_file: str):
    """Write process ID to file."""
    try:
        with open(pid_file, "w") as f:
            f.write(str(os.getpid()))
        logger.info(f"PID file written: {pid_file}")
    except Exception as e:
        logger.error(f"Failed to write PID file {pid_file}: {e}")


def remove_pid_file(pid_file: str):
    """Remove PID file."""
    try:
        if os.path.exists(pid_file):
            os.remove(pid_file)
            logger.info(f"PID file removed: {pid_file}")
    except Exception as e:
        logger.error(f"Failed to remove PID file {pid_file}: {e}")


def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(
        description="Production Scheduler Service Runner",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog=__doc__,
    )

    parser.add_argument(
        "--redis-url",
        default=os.getenv("REDIS_URL", "redis://localhost:6379"),
        help="Redis connection URL",
    )

    parser.add_argument(
        "--max-schedulers", type=int, default=10, help="Maximum concurrent schedulers"
    )

    parser.add_argument(
        "--batch-size", type=int, default=50, help="Scheduler batch processing size"
    )

    parser.add_argument(
        "--worker-concurrency", type=int, default=5, help="Task worker concurrency"
    )

    parser.add_argument(
        "--cycle-interval",
        type=int,
        default=30,
        help="Scheduler cycle interval in seconds",
    )

    parser.add_argument(
        "--log-level",
        choices=["DEBUG", "INFO", "WARNING", "ERROR"],
        default="INFO",
        help="Logging level",
    )

    parser.add_argument(
        "--metrics-port", type=int, default=8080, help="Port for metrics endpoint"
    )

    parser.add_argument(
        "--health-port", type=int, default=8081, help="Port for health check endpoint"
    )

    parser.add_argument("--daemon", action="store_true", help="Run as daemon process")

    parser.add_argument(
        "--pid-file",
        default="/tmp/scheduler_service.pid",
        help="PID file path for daemon mode",
    )

    return parser.parse_args()


async def main():
    """Main entry point."""
    args = parse_args()

    # Setup logging
    setup_logging(log_level=args.log_level, log_format="text")

    # Write PID file if specified
    if args.pid_file:
        write_pid_file(args.pid_file)

    try:
        # Create and run the service
        runner = SchedulerServiceRunner(args)
        await runner.run()

    except KeyboardInterrupt:
        logger.info("Received keyboard interrupt, shutting down...")
    except Exception as e:
        logger.error(f"Fatal error: {e}", exc_info=True)
        sys.exit(1)
    finally:
        # Remove PID file
        if args.pid_file:
            remove_pid_file(args.pid_file)


if __name__ == "__main__":
    # Handle daemon mode
    args = parse_args()

    if args.daemon:
        # Simple daemonization
        if os.fork() > 0:
            sys.exit(0)  # Parent exits

        os.setsid()  # Create new session

        if os.fork() > 0:
            sys.exit(0)  # Second parent exits

        # Redirect standard file descriptors
        sys.stdout.flush()
        sys.stderr.flush()

        with open("/dev/null", "r") as f:
            os.dup2(f.fileno(), sys.stdin.fileno())
        with open("/dev/null", "w") as f:
            os.dup2(f.fileno(), sys.stdout.fileno())
            os.dup2(f.fileno(), sys.stderr.fileno())

    # Run the main function
    asyncio.run(main())
