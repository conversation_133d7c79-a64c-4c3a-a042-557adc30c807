"""
Unified Google Drive Adapter for the Trigger Service.

This module implements a unified Google Drive adapter that can handle both:
- User OAuth credentials for individual user monitoring
- Service account credentials for organization-wide monitoring

The adapter automatically detects the credential type and uses the appropriate
authentication method and API endpoints.
"""

import asyncio
import json
import os
from datetime import datetime, timedelta, timezone
from typing import Any, Dict, List, Optional, Set, Union
from uuid import UUID

import httpx
from google.auth.transport.requests import Request
from google.oauth2.credentials import Credentials as UserCredentials
from google.oauth2.service_account import Credentials as ServiceAccountCredentials
from googleapiclient.discovery import build
from googleapiclient.errors import HttpError
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, update

from src.adapters.base import (
    BaseTriggerAdapter,
    TriggerEvent,
    TriggerEventType,
    TriggerConfiguration,
    AdapterHealthStatus,
)
from src.database.connection import get_async_session
from src.database.models import (
    <PERSON><PERSON>vents,
    Trigger,
)
from src.utils.config import get_settings
from src.utils.logger import get_logger
from src.utils.retry import Re<PERSON><PERSON><PERSON><PERSON>, RetryableError

logger = get_logger(__name__)


class GoogleDriveUnifiedError(Exception):
    """Base exception for Google Drive unified adapter errors."""

    pass


class GoogleDriveUnifiedAuthError(GoogleDriveUnifiedError):
    """Authentication-related errors."""

    pass


class GoogleDriveUnifiedAPIError(GoogleDriveUnifiedError, RetryableError):
    """API-related errors that should trigger retry."""

    pass


class CredentialType:
    """Enumeration for credential types."""

    USER = "user"
    SERVICE_ACCOUNT = "service_account"


class GoogleDriveUnifiedAdapter(BaseTriggerAdapter):
    """
    Unified Google Drive adapter for both user and service account monitoring.

    This adapter can handle:
    - User OAuth credentials for individual user drive monitoring
    - Service account credentials for organization-wide drive monitoring

    It automatically detects the credential type from the configuration and
    uses the appropriate authentication method.
    """

    _instance = None

    def __new__(cls):
        """Ensure singleton pattern for GoogleDriveUnifiedAdapter."""
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance

    @classmethod
    def get_instance(cls):
        """Get the singleton instance of GoogleDriveUnifiedAdapter."""
        if cls._instance is None:
            cls._instance = cls()
        return cls._instance

    def __init__(self):
        """Initialize the unified Google Drive adapter."""
        # Only initialize once (singleton pattern)
        if hasattr(self, "_initialized"):
            return

        super().__init__("google_drive_unified")
        self.settings = get_settings()

        self.retry_handler = RetryHandler(
            retryable_exceptions=[
                GoogleDriveUnifiedAPIError,
                HttpError,
                ConnectionError,
            ]
        )

        # Multi-user webhook and polling management
        self._webhook_subscriptions: Dict[UUID, Dict[str, Any]] = {}
        self._polling_tasks: Dict[UUID, asyncio.Task] = {}
        self._polling_states: Dict[UUID, Dict[str, Any]] = {}

        # Credential management for both types
        self._user_credentials: Dict[str, UserCredentials] = {}
        self._service_account_credentials: Optional[ServiceAccountCredentials] = None
        self._services: Dict[str, Any] = {}

        # Channel management
        self._active_channels: Dict[str, Dict[str, Any]] = {}

        # Mark as initialized
        self._initialized = True

        logger.info(
            "Google Drive unified adapter initialized",
            active_channels_count=len(self._active_channels),
        )

    @property
    def supported_event_types(self) -> Set[TriggerEventType]:
        """
        Get the set of event types supported by this adapter.

        Returns:
            Set[TriggerEventType]: Supported event types
        """
        return {
            TriggerEventType.CREATED,
            TriggerEventType.UPDATED,
            TriggerEventType.DELETED,
            TriggerEventType.MOVED,
        }

    async def validate_config(self, config: Dict[str, Any]) -> bool:
        """
        Validate Google Drive unified adapter configuration.

        Args:
            config: Configuration to validate

        Returns:
            bool: True if configuration is valid, False otherwise
        """
        try:
            # Determine credential type
            credential_type = self._determine_credential_type(config)

            if credential_type == CredentialType.USER:
                # Validate user configuration
                required_fields = ["user_id"]
                for field in required_fields:
                    if field not in config:
                        logger.error(
                            f"Missing required field for user credentials: {field}"
                        )
                        return False

                # Validate user_id format
                user_id = config["user_id"]
                if not isinstance(user_id, str) or not user_id.strip():
                    logger.error("user_id must be a non-empty string")
                    return False

            elif credential_type == CredentialType.SERVICE_ACCOUNT:
                # Validate service account configuration
                required_fields = ["service_account_file"]
                for field in required_fields:
                    if field not in config:
                        logger.error(
                            f"Missing required field for service account: {field}"
                        )
                        return False

                # Validate service account file exists
                service_account_file = config["service_account_file"]
                if not os.path.exists(service_account_file):
                    logger.error(
                        f"Service account file not found: {service_account_file}"
                    )
                    return False

            else:
                logger.error(
                    "Invalid credential type. Must specify either user_id or service_account_file"
                )
                return False

            # Optional fields validation
            if "folder_ids" in config:
                folder_ids = config["folder_ids"]
                if not isinstance(folder_ids, list):
                    logger.error("folder_ids must be a list")
                    return False

            if "file_types" in config:
                file_types = config["file_types"]
                if not isinstance(file_types, list):
                    logger.error("file_types must be a list")
                    return False

            # Validate webhook_ttl if provided
            if "webhook_ttl" in config:
                webhook_ttl = config["webhook_ttl"]
                if not isinstance(webhook_ttl, int) or webhook_ttl <= 0:
                    logger.error("webhook_ttl must be a positive integer")
                    return False

            logger.debug(
                f"Configuration validation passed for credential type: {credential_type}"
            )
            return True

        except Exception as e:
            logger.error(f"Error validating configuration: {str(e)}")
            return False

    def _determine_credential_type(self, config: Dict[str, Any]) -> str:
        """
        Determine the credential type from configuration.

        Args:
            config: Configuration dictionary

        Returns:
            str: Credential type (user or service_account)
        """
        if "user_id" in config:
            return CredentialType.USER
        elif "service_account_file" in config:
            return CredentialType.SERVICE_ACCOUNT
        else:
            raise GoogleDriveUnifiedError(
                "Cannot determine credential type from configuration"
            )

    async def setup_trigger(
        self, trigger_config: TriggerConfiguration, session=None
    ) -> bool:
        """
        Set up a new Google Drive trigger with unified credential support.

        Args:
            trigger_config: Complete trigger configuration
            session: Optional database session

        Returns:
            bool: True if setup was successful, False otherwise
        """
        try:
            credential_type = self._determine_credential_type(trigger_config.config)

            logger.info(
                "🔧 Setting up Google Drive unified trigger",
                trigger_id=trigger_config.trigger_id,
                credential_type=credential_type,
                user_id=trigger_config.config.get("user_id"),
                service_account_file=trigger_config.config.get("service_account_file"),
                folder_ids=trigger_config.config.get("folder_ids"),
                event_types=trigger_config.event_types,
                use_polling=trigger_config.config.get("use_polling", False),
            )

            # Get appropriate credentials
            credentials = await self._get_credentials(trigger_config.config)
            if not credentials:
                logger.error(
                    "❌ Failed to get Google Drive credentials",
                    credential_type=credential_type,
                    trigger_id=trigger_config.trigger_id,
                )
                return False

            # Create Google Drive service
            service = await self._create_drive_service(credentials)
            if not service:
                logger.error("Failed to create Google Drive service")
                return False

            # Check if polling mode is explicitly requested or if webhook setup fails
            use_polling = trigger_config.config.get("use_polling", False)

            if not use_polling:
                # Try webhook setup first
                subscription_id = await self._create_webhook_subscription(
                    service, trigger_config, session
                )
                if subscription_id:
                    # Get the resource_id from the active channel
                    channel_info = self._active_channels.get(subscription_id)
                    resource_id = (
                        channel_info.get("resource_id")
                        if channel_info
                        else subscription_id
                    )

                    # Store subscription information
                    self._webhook_subscriptions[trigger_config.trigger_id] = {
                        "subscription_id": subscription_id,
                        "credential_type": credential_type,
                        "user_id": trigger_config.config.get("user_id"),
                        "service_account_file": trigger_config.config.get(
                            "service_account_file"
                        ),
                        "folder_ids": trigger_config.config.get("folder_ids", []),
                        "created_at": datetime.now(),
                        "expires_at": datetime.now()
                        + timedelta(
                            seconds=trigger_config.config.get("webhook_ttl", 3600)
                        ),
                        "resource_id": resource_id,
                    }

                    logger.info(
                        "✅ Google Drive unified webhook trigger setup successful",
                        trigger_id=trigger_config.trigger_id,
                        credential_type=credential_type,
                        subscription_id=subscription_id,
                        folder_ids=trigger_config.config.get("folder_ids"),
                    )
                    return True
                else:
                    logger.warning(
                        "⚠️ Webhook setup failed, falling back to polling",
                        trigger_id=trigger_config.trigger_id,
                        credential_type=credential_type,
                    )
                    use_polling = True

            if use_polling:
                # Set up polling mode
                success = await self._setup_polling_trigger(trigger_config, service)
                if success:
                    logger.info(
                        "✅ Google Drive unified polling trigger setup successful",
                        trigger_id=trigger_config.trigger_id,
                        credential_type=credential_type,
                        poll_interval=trigger_config.config.get(
                            "poll_interval_seconds", 300
                        ),
                        folder_ids=trigger_config.config.get("folder_ids"),
                    )
                    return True

            logger.error(
                "Failed to set up trigger with both webhook and polling methods"
            )
            return False

        except Exception as e:
            logger.error(
                f"Failed to setup Google Drive unified trigger {trigger_config.trigger_id}",
                error=str(e),
            )
            return False

    async def _get_credentials(
        self, config: Dict[str, Any]
    ) -> Optional[Union[UserCredentials, ServiceAccountCredentials]]:
        """
        Get appropriate credentials based on configuration.

        Args:
            config: Configuration dictionary

        Returns:
            Union[UserCredentials, ServiceAccountCredentials]: Appropriate credentials
        """
        try:
            credential_type = self._determine_credential_type(config)

            if credential_type == CredentialType.USER:
                return await self._get_user_credentials(config["user_id"])
            elif credential_type == CredentialType.SERVICE_ACCOUNT:
                return await self._get_service_account_credentials(
                    config["service_account_file"]
                )
            else:
                logger.error(f"Unknown credential type: {credential_type}")
                return None

        except Exception as e:
            logger.error(f"Failed to get credentials: {str(e)}")
            return None

    async def _get_user_credentials(self, user_id: str) -> Optional[UserCredentials]:
        """
        Get Google Drive user credentials for a user with caching support.

        Args:
            user_id: ID of the user

        Returns:
            UserCredentials: Google OAuth2 credentials, or None if not found
        """
        try:
            # Check if we have cached credentials for this user
            if user_id in self._user_credentials:
                credentials = self._user_credentials[user_id]

                # Check if credentials are still valid
                if credentials.valid:
                    return credentials

                # Try to refresh if expired
                if credentials.expired and credentials.refresh_token:
                    logger.info(
                        "Refreshing cached credentials for user", user_id=user_id
                    )
                    try:
                        loop = asyncio.get_event_loop()
                        await loop.run_in_executor(None, credentials.refresh, Request())
                        logger.info(
                            f"Successfully refreshed credentials for user {user_id}"
                        )
                        return credentials
                    except Exception as refresh_error:
                        logger.error(
                            f"Failed to refresh cached credentials: {refresh_error}"
                        )
                        # Remove invalid credentials from cache
                        del self._user_credentials[user_id]

            # Load fresh credentials from auth service
            credentials = await self._load_user_credentials_from_auth_service(user_id)
            if credentials:
                # Cache the credentials for future use
                self._user_credentials[user_id] = credentials
                logger.info("Cached credentials for user", user_id=user_id)
                return credentials

            return None

        except Exception as e:
            logger.error(f"Failed to get credentials for user {user_id}", error=str(e))
            return None

    async def _load_user_credentials_from_auth_service(
        self, user_id: str
    ) -> Optional[UserCredentials]:
        """
        Load user credentials from auth service.

        Args:
            user_id: ID of the user

        Returns:
            UserCredentials: Google user credentials, or None if not found
        """
        try:
            from src.utils.auth_client import AuthClient, AuthServiceConnectionError

            logger.info(
                f"Loading Google Drive user credentials for user {user_id} from auth service"
            )

            # Try to fetch credentials from auth service
            try:
                # Create auth client instance and use async context manager
                auth_client = AuthClient()
                async with auth_client:
                    # Fetch OAuth credentials from auth service for Google Drive
                    oauth_credentials = await auth_client.get_oauth_credentials(
                        user_id, tool_name="google_drive", provider="google"
                    )

                    if not oauth_credentials:
                        logger.warning(
                            f"No OAuth credentials found for user {user_id} in auth service"
                        )
                        return None

                    logger.debug(
                        "OAuth credentials received",
                        oauth_credentials=oauth_credentials,
                    )

                    # Extract Google Drive credentials from OAuth response
                    if not oauth_credentials.get("success"):
                        logger.warning(
                            f"OAuth credentials request was not successful for user {user_id}"
                        )
                        return None

                    # Validate required fields
                    required_fields = ["access_token"]
                    for field in required_fields:
                        if field not in oauth_credentials:
                            logger.error(
                                f"Missing required OAuth field '{field}' for user {user_id}"
                            )
                            return None

                    # Extract scopes from the response (convert space-separated string to list)
                    scope_string = oauth_credentials.get("scope", "")
                    scopes = (
                        scope_string.split()
                        if scope_string
                        else [
                            "https://www.googleapis.com/auth/drive",
                            "https://www.googleapis.com/auth/drive.file",
                            "https://www.googleapis.com/auth/drive.metadata",
                        ]
                    )

                    # Create Google OAuth2 credentials object
                    credentials = UserCredentials(
                        token=oauth_credentials.get("access_token"),
                        refresh_token=oauth_credentials.get("refresh_token"),
                        token_uri="https://oauth2.googleapis.com/token",
                        client_id=(
                            self.settings.google_client_id
                            if hasattr(self.settings, "google_client_id")
                            else None
                        ),
                        client_secret=(
                            self.settings.google_client_secret
                            if hasattr(self.settings, "google_client_secret")
                            else None
                        ),
                        scopes=scopes,
                    )

                    # Check if token needs refresh
                    if credentials.expired and credentials.refresh_token:
                        logger.info(
                            "Refreshing expired token for user", user_id=user_id
                        )
                        try:
                            # Use asyncio to refresh token without blocking
                            loop = asyncio.get_event_loop()
                            await loop.run_in_executor(
                                None, credentials.refresh, Request()
                            )

                            # TODO: Update refreshed token back to auth service
                            logger.info("Token refreshed for user", user_id=user_id)
                            logger.warning(
                                f"Refreshed token not saved back to auth service - implement update endpoint"
                            )

                        except Exception as refresh_error:
                            logger.error(
                                f"Failed to refresh token for user {user_id}: {refresh_error}"
                            )
                            return None
                    elif credentials.expired:
                        logger.error(
                            f"Token expired and no refresh token available for user {user_id}"
                        )
                        return None

                    logger.info(
                        f"Successfully loaded credentials for user {user_id} from auth service"
                    )
                    return credentials

            except AuthServiceConnectionError as conn_error:
                logger.warning(
                    f"Auth service connection failed for user {user_id}: {conn_error}."
                )
                return None
            except Exception as auth_error:
                logger.error(f"Auth service error for user {user_id}: {auth_error}.")
                return None

        except Exception as e:
            logger.error(
                f"Failed to load credentials for user {user_id} from auth service",
                error=str(e),
            )
            return None

    async def _get_service_account_credentials(
        self, service_account_file: str
    ) -> Optional[ServiceAccountCredentials]:
        """
        Get Google Drive service account credentials.

        Args:
            service_account_file: Path to service account JSON file

        Returns:
            ServiceAccountCredentials: Google service account credentials, or None if failed
        """
        try:
            # Check if we have cached service account credentials
            if (
                self._service_account_credentials
                and self._service_account_credentials.valid
            ):
                return self._service_account_credentials

            # Load service account credentials from file
            if not os.path.exists(service_account_file):
                logger.error(f"Service account file not found: {service_account_file}")
                return None

            scopes = [
                "https://www.googleapis.com/auth/drive",
                "https://www.googleapis.com/auth/drive.file",
                "https://www.googleapis.com/auth/drive.metadata",
            ]

            credentials = ServiceAccountCredentials.from_service_account_file(
                service_account_file, scopes=scopes
            )

            # Cache the credentials
            self._service_account_credentials = credentials
            logger.info(
                f"Successfully loaded service account credentials from {service_account_file}"
            )
            return credentials

        except Exception as e:
            logger.error(f"Failed to load service account credentials: {str(e)}")
            return None

    async def _create_drive_service(
        self, credentials: Union[UserCredentials, ServiceAccountCredentials]
    ):
        """
        Create Google Drive API service instance.

        Args:
            credentials: Google credentials (user or service account)

        Returns:
            Google Drive service instance, or None if failed
        """
        try:
            service = build("drive", "v3", credentials=credentials)
            return service

        except Exception as e:
            logger.error(f"Failed to create Google Drive service", error=str(e))
            return None

    async def _create_webhook_subscription(
        self, service, trigger_config: TriggerConfiguration, session=None
    ) -> Optional[str]:
        """
        Create a webhook subscription for drive events.

        Args:
            service: Google Drive service instance
            trigger_config: Trigger configuration
            session: Optional database session

        Returns:
            str: Subscription ID if successful, None otherwise
        """
        try:
            credential_type = self._determine_credential_type(trigger_config.config)
            folder_ids = trigger_config.config.get("folder_ids", [])
            webhook_ttl = trigger_config.config.get("webhook_ttl", 604800)  # 7 days

            # Determine webhook URL based on credential type
            if credential_type == CredentialType.USER:
                webhook_url_attr = "google_drive_webhook_url"
                webhook_secret_attr = "google_drive_webhook_secret"
            else:
                webhook_url_attr = "google_drive_webhook_url"
                webhook_secret_attr = "google_drive_webhook_secret"

            # Check if webhook URL is configured
            if not hasattr(self.settings, webhook_url_attr) or not getattr(
                self.settings, webhook_url_attr
            ):
                logger.error(
                    f"Google Drive webhook URL not configured: {webhook_url_attr}"
                )
                return None

            # Validate that webhook URL is HTTPS (required by Google Drive)
            webhook_url = getattr(self.settings, webhook_url_attr)
            logger.info("Using webhook URL", webhook_url=webhook_url)

            if not webhook_url.startswith("https://"):
                logger.error(
                    f"Google Drive webhook URL must use HTTPS, got: {webhook_url}",
                    extra={
                        "help": "For development, use ngrok to create an HTTPS tunnel: 'ngrok http 8000'"
                    },
                )
                return None

            # Generate unique channel ID
            import uuid

            channel_id = f"drive-{credential_type}-trigger-{trigger_config.trigger_id}-{str(uuid.uuid4())[:8]}"

            # Calculate expiration time
            expiration = int(
                (datetime.now(timezone.utc).timestamp() + webhook_ttl) * 1000
            )

            # Prepare webhook subscription request
            body = {
                "id": channel_id,
                "type": "web_hook",
                "address": webhook_url,
                "expiration": str(expiration),
                "params": {
                    "ttl": str(webhook_ttl),
                },
            }

            # Add token for verification if configured
            webhook_secret = getattr(self.settings, webhook_secret_attr, None)
            if webhook_secret:
                body["token"] = webhook_secret

            # Create the subscription with timeout
            # For Drive, we watch changes to the entire drive or specific folders
            result = None
            if folder_ids:
                # Watch specific folders
                for folder_id in folder_ids:
                    result = await asyncio.wait_for(
                        self._execute_watch_request(service, folder_id, body),
                        timeout=30.0,
                    )
                    if result:
                        break
            else:
                # Watch entire drive
                result = await asyncio.wait_for(
                    self._execute_watch_request(service, "root", body),
                    timeout=30.0,
                )

            if result:
                subscription_id = result.get("id")
                resource_id = result.get("resourceId")

                # Save channel_id to database
                try:
                    if session:
                        # Use the passed session
                        await session.execute(
                            update(Trigger)
                            .where(Trigger.id == trigger_config.trigger_id)
                            .values(channel_id=channel_id)
                        )
                    else:
                        # Fallback: create our own session
                        async for db_session in get_async_session():
                            await db_session.execute(
                                update(Trigger)
                                .where(Trigger.id == trigger_config.trigger_id)
                                .values(channel_id=channel_id)
                            )
                            await db_session.commit()
                            break

                    logger.info(
                        "Successfully saved channel_id to database",
                        channel_id=channel_id,
                        resource_id=resource_id,
                        trigger_id=trigger_config.trigger_id,
                        credential_type=credential_type,
                        folder_ids=folder_ids,
                    )

                    # Store channel info for management
                    self._active_channels[channel_id] = {
                        "resource_id": resource_id,
                        "expiration": expiration,
                        "trigger_id": trigger_config.trigger_id,
                        "credential_type": credential_type,
                        "user_id": trigger_config.config.get("user_id"),
                        "service_account_file": trigger_config.config.get(
                            "service_account_file"
                        ),
                        "folder_ids": folder_ids,
                        "event_types": trigger_config.event_types,
                        "created_at": datetime.now(timezone.utc).timestamp(),
                    }

                    return channel_id

                except Exception as db_error:
                    logger.error(
                        "Failed to save channel_id to database",
                        error=str(db_error),
                        channel_id=channel_id,
                        trigger_id=trigger_config.trigger_id,
                    )
                    # Fall back to in-memory storage if database fails
                    self._active_channels[channel_id] = {
                        "resource_id": resource_id,
                        "expiration": expiration,
                        "trigger_id": trigger_config.trigger_id,
                        "credential_type": credential_type,
                        "user_id": trigger_config.config.get("user_id"),
                        "service_account_file": trigger_config.config.get(
                            "service_account_file"
                        ),
                        "folder_ids": folder_ids,
                        "event_types": trigger_config.event_types,
                        "created_at": datetime.now(timezone.utc).timestamp(),
                    }
                    return channel_id

            return None

        except asyncio.TimeoutError:
            logger.error("Timeout creating webhook subscription")
            return None
        except Exception as e:
            logger.error(f"Failed to create webhook subscription", error=str(e))
            return None

    async def _execute_watch_request(
        self, service, resource_id: str, body: Dict[str, Any]
    ):
        """
        Execute the Google Drive watch request.

        Args:
            service: Google Drive service instance
            resource_id: Resource ID to watch (folder ID or "root")
            body: Request body for the watch request

        Returns:
            Dict: Response from the watch request
        """
        try:
            # Execute the watch request asynchronously
            loop = asyncio.get_event_loop()
            result = await loop.run_in_executor(
                None,
                lambda: service.files().watch(fileId=resource_id, body=body).execute(),
            )
            return result

        except HttpError as e:
            if e.resp.status in [500, 502, 503, 504]:
                # Retryable HTTP errors
                raise GoogleDriveUnifiedAPIError(f"Google Drive API error: {str(e)}")
            else:
                # Non-retryable errors
                raise GoogleDriveUnifiedError(f"Google Drive API error: {str(e)}")
        except Exception as e:
            raise GoogleDriveUnifiedAPIError(f"Unexpected error: {str(e)}")

    async def process_event(self, raw_event: Dict[str, Any]) -> Optional[TriggerEvent]:
        """
        Process a raw Google Drive webhook event.

        Args:
            raw_event: Raw event data from Google Drive webhook

        Returns:
            TriggerEvent: Standardized event data, or None if event should be ignored
        """
        try:
            logger.info(
                "📨 Processing Google Drive unified webhook event",
                event_type=raw_event.get("type"),
                has_headers=bool(raw_event.get("headers")),
                has_webhook_headers=bool(raw_event.get("webhook_headers")),
                channel_id=raw_event.get("webhook_headers", {}).get(
                    "x-goog-channel-id"
                ),
                resource_state=raw_event.get("webhook_headers", {}).get(
                    "x-goog-resource-state"
                ),
            )

            # Handle verification events (sync messages)
            if raw_event.get("type") == "verification":
                logger.info("Received webhook verification/sync event")
                return None

            # Extract webhook headers for validation
            headers = raw_event.get("webhook_headers", {})

            if not headers:
                logger.warning("No webhook_headers found in event")
                return None

            # Validate webhook authenticity
            if not self._validate_webhook(headers, raw_event):
                logger.warning("Invalid webhook received, ignoring")
                return None

            # Get channel information to identify the trigger
            channel_id = headers.get("x-goog-channel-id")

            if not channel_id:
                logger.warning("Missing channel ID in webhook headers")
                return None

            try:
                trigger_info = None
                async for session in get_async_session():
                    # Query database for trigger with this channel_id
                    result = await session.execute(
                        select(Trigger).where(Trigger.channel_id == channel_id)
                    )
                    trigger_record = result.scalar_one_or_none()

                    if trigger_record:
                        trigger_info = {
                            "trigger_id": trigger_record.id,
                            "user_id": trigger_record.trigger_config.get("user_id"),
                            "service_account_file": trigger_record.trigger_config.get(
                                "service_account_file"
                            ),
                            "folder_ids": trigger_record.trigger_config.get(
                                "folder_ids", []
                            ),
                            "event_types": trigger_record.event_types,
                            "is_active": trigger_record.is_active,
                            "workflow_id": trigger_record.workflow_id,
                            "credential_type": self._determine_credential_type(
                                trigger_record.trigger_config
                            ),
                        }
                    break

                if not trigger_info:
                    logger.warning(
                        "No trigger found for channel_id",
                        channel_id=channel_id,
                    )
                    return None

                if not trigger_info["is_active"]:
                    logger.warning(
                        "Trigger is inactive for channel_id",
                        trigger_id=trigger_info["trigger_id"],
                        channel_id=channel_id,
                    )
                    return None

                # Extract trigger information from database result
                trigger_id = trigger_info["trigger_id"]
                folder_ids = trigger_info["folder_ids"]
                configured_event_types = trigger_info["event_types"]
                workflow_id = trigger_info["workflow_id"]
                credential_type = trigger_info["credential_type"]

                # Get resource state from headers
                resource_state = headers.get("x-goog-resource-state", "").lower()

                # Map Google Drive resource states to our event types
                event_type_mapping = {
                    "sync": None,  # Ignore sync events
                    "add": TriggerEventType.CREATED,
                    "remove": TriggerEventType.DELETED,
                    "update": TriggerEventType.UPDATED,
                    "trash": TriggerEventType.DELETED,
                }

                event_type = event_type_mapping.get(resource_state)

                if not event_type:
                    logger.debug(
                        f"Ignoring event with resource state: {resource_state}",
                        channel_id=channel_id,
                    )
                    return None

                # Check if this event type is configured for the trigger
                if event_type not in configured_event_types:
                    logger.debug(
                        f"Event type {event_type} not configured for trigger",
                        trigger_id=trigger_id,
                        configured_types=configured_event_types,
                    )
                    return None

                # Get resource ID and URI from headers
                resource_id = headers.get("x-goog-resource-id")
                resource_uri = headers.get("x-goog-resource-uri")

                # Get file details from Google Drive API
                file_details = await self._get_file_details(
                    trigger_info, resource_id, resource_uri
                )

                if not file_details:
                    logger.warning(
                        "Could not retrieve file details for event",
                        resource_id=resource_id,
                        channel_id=channel_id,
                    )
                    return None

                # Check if file is in monitored folders (if specified)
                if folder_ids and not self._is_file_in_folders(
                    file_details, folder_ids
                ):
                    logger.debug(
                        "File not in monitored folders, ignoring",
                        file_id=file_details.get("id"),
                        file_name=file_details.get("name"),
                        folder_ids=folder_ids,
                    )
                    return None

                # Create standardized event
                trigger_event = TriggerEvent(
                    trigger_id=trigger_id,
                    workflow_id=workflow_id,
                    event_type=event_type,
                    event_data={
                        "file_id": file_details.get("id"),
                        "file_name": file_details.get("name"),
                        "file_type": file_details.get("mimeType"),
                        "file_size": file_details.get("size"),
                        "modified_time": file_details.get("modifiedTime"),
                        "created_time": file_details.get("createdTime"),
                        "owners": file_details.get("owners", []),
                        "parents": file_details.get("parents", []),
                        "web_view_link": file_details.get("webViewLink"),
                        "credential_type": credential_type,
                        "user_id": trigger_info.get("user_id"),
                        "service_account_file": trigger_info.get(
                            "service_account_file"
                        ),
                        "resource_state": resource_state,
                        "channel_id": channel_id,
                    },
                    timestamp=datetime.now(timezone.utc),
                    raw_event=raw_event,
                )

                logger.info(
                    "✅ Successfully processed Google Drive unified webhook event",
                    trigger_id=trigger_id,
                    event_type=event_type,
                    file_name=file_details.get("name"),
                    file_id=file_details.get("id"),
                    credential_type=credential_type,
                    resource_state=resource_state,
                )

                return trigger_event

            except Exception as db_error:
                logger.error(
                    f"Database error while processing event: {str(db_error)}",
                    channel_id=channel_id,
                )
                return None

        except Exception as e:
            logger.error(f"Error processing Google Drive unified event: {str(e)}")
            return None

    def _validate_webhook(
        self, headers: Dict[str, str], raw_event: Dict[str, Any]
    ) -> bool:
        """
        Validate Google Drive webhook authenticity.

        Args:
            headers: Webhook headers
            raw_event: Raw event data

        Returns:
            bool: True if webhook is valid, False otherwise
        """
        try:
            # Check required headers
            required_headers = ["x-goog-channel-id", "x-goog-resource-state"]
            for header in required_headers:
                if header not in headers:
                    logger.warning(f"Missing required header: {header}")
                    return False

            # Validate channel ID format
            channel_id = headers.get("x-goog-channel-id")
            if not channel_id or not channel_id.startswith("drive-"):
                logger.warning(f"Invalid channel ID format: {channel_id}")
                return False

            # Check if we have this channel registered
            if channel_id not in self._active_channels:
                logger.warning(f"Unknown channel ID: {channel_id}")
                return False

            # Check channel expiration
            channel_info = self._active_channels[channel_id]
            expiration = channel_info.get("expiration", 0)
            current_time = datetime.now(timezone.utc).timestamp() * 1000

            if current_time > expiration:
                logger.warning(
                    f"Channel expired: {channel_id}",
                    expiration=expiration,
                    current_time=current_time,
                )
                # Remove expired channel
                del self._active_channels[channel_id]
                return False

            # Validate webhook token if configured
            credential_type = channel_info.get("credential_type", CredentialType.USER)
            if credential_type == CredentialType.USER:
                webhook_secret_attr = "google_drive_webhook_secret"
            else:
                webhook_secret_attr = "google_drive_webhook_secret"

            webhook_secret = getattr(self.settings, webhook_secret_attr, None)
            if webhook_secret:
                token = headers.get("x-goog-channel-token")
                if token != webhook_secret:
                    logger.warning("Invalid webhook token")
                    return False

            return True

        except Exception as e:
            logger.error(f"Error validating webhook: {str(e)}")
            return False

    async def _get_file_details(
        self, trigger_info: Dict[str, Any], resource_id: str, resource_uri: str
    ) -> Optional[Dict[str, Any]]:
        """
        Get file details from Google Drive API.

        Args:
            trigger_info: Trigger information
            resource_id: Resource ID from webhook
            resource_uri: Resource URI from webhook

        Returns:
            Dict: File details, or None if failed
        """
        try:
            # Get appropriate credentials
            config = {
                "user_id": trigger_info.get("user_id"),
                "service_account_file": trigger_info.get("service_account_file"),
            }
            credentials = await self._get_credentials(config)
            if not credentials:
                logger.error("Failed to get credentials for file details")
                return None

            # Create service
            service = await self._create_drive_service(credentials)
            if not service:
                logger.error("Failed to create service for file details")
                return None

            # Extract file ID from resource URI if needed
            file_id = resource_id
            if resource_uri and not file_id:
                # Parse file ID from URI like: https://www.googleapis.com/drive/v3/files/FILE_ID
                import re

                match = re.search(r"/files/([^/?]+)", resource_uri)
                if match:
                    file_id = match.group(1)

            if not file_id:
                logger.error("Could not determine file ID")
                return None

            # Get file details
            loop = asyncio.get_event_loop()
            file_details = await loop.run_in_executor(
                None,
                lambda: service.files()
                .get(
                    fileId=file_id,
                    fields="id,name,mimeType,size,modifiedTime,createdTime,owners,parents,webViewLink",
                )
                .execute(),
            )

            return file_details

        except HttpError as e:
            if e.resp.status == 404:
                logger.warning(f"File not found: {resource_id}")
                return None
            else:
                logger.error(f"Google Drive API error getting file details: {str(e)}")
                return None
        except Exception as e:
            logger.error(f"Error getting file details: {str(e)}")
            return None

    def _is_file_in_folders(
        self, file_details: Dict[str, Any], folder_ids: List[str]
    ) -> bool:
        """
        Check if file is in any of the monitored folders.

        Args:
            file_details: File details from Google Drive API
            folder_ids: List of folder IDs to monitor

        Returns:
            bool: True if file is in monitored folders, False otherwise
        """
        try:
            file_parents = file_details.get("parents", [])

            # Check if any parent folder is in our monitored list
            for parent_id in file_parents:
                if parent_id in folder_ids:
                    return True

            return False

        except Exception as e:
            logger.error(f"Error checking file folders: {str(e)}")
            return False

    async def _setup_polling_trigger(
        self, trigger_config: TriggerConfiguration, service
    ) -> bool:
        """
        Set up polling-based trigger for Google Drive.

        Args:
            trigger_config: Trigger configuration
            service: Google Drive service instance

        Returns:
            bool: True if setup was successful, False otherwise
        """
        try:
            trigger_id = trigger_config.trigger_id
            poll_interval = trigger_config.config.get("poll_interval_seconds", 300)
            folder_ids = trigger_config.config.get("folder_ids", [])
            credential_type = self._determine_credential_type(trigger_config.config)

            # Initialize polling state
            self._polling_states[trigger_id] = {
                "last_poll": datetime.now(timezone.utc),
                "page_token": None,
                "credential_type": credential_type,
                "user_id": trigger_config.config.get("user_id"),
                "service_account_file": trigger_config.config.get(
                    "service_account_file"
                ),
                "folder_ids": folder_ids,
                "event_types": trigger_config.event_types,
                "workflow_id": trigger_config.workflow_id,
            }

            # Start polling task
            polling_task = asyncio.create_task(
                self._poll_drive_changes(trigger_id, service, poll_interval)
            )
            self._polling_tasks[trigger_id] = polling_task

            logger.info(
                f"Started polling task for trigger {trigger_id}",
                poll_interval=poll_interval,
                credential_type=credential_type,
                folder_ids=folder_ids,
            )

            return True

        except Exception as e:
            logger.error(f"Failed to setup polling trigger: {str(e)}")
            return False

    async def _poll_drive_changes(self, trigger_id: UUID, service, poll_interval: int):
        """
        Poll Google Drive for changes.

        Args:
            trigger_id: Trigger ID
            service: Google Drive service instance
            poll_interval: Polling interval in seconds
        """
        try:
            while trigger_id in self._polling_states:
                try:
                    await self._check_drive_changes(trigger_id, service)
                    await asyncio.sleep(poll_interval)

                except Exception as e:
                    logger.error(
                        f"Error in polling loop for trigger {trigger_id}: {str(e)}"
                    )
                    await asyncio.sleep(min(poll_interval, 60))  # Back off on error

        except asyncio.CancelledError:
            logger.info(f"Polling task cancelled for trigger {trigger_id}")
        except Exception as e:
            logger.error(
                f"Fatal error in polling task for trigger {trigger_id}: {str(e)}"
            )

    async def _check_drive_changes(self, trigger_id: UUID, service):
        """
        Check for changes in Google Drive.

        Args:
            trigger_id: Trigger ID
            service: Google Drive service instance
        """
        try:
            polling_state = self._polling_states.get(trigger_id)
            if not polling_state:
                return

            # Get changes since last poll
            loop = asyncio.get_event_loop()

            # Use changes.list API to get recent changes
            request_params = {
                "pageToken": polling_state.get("page_token"),
                "fields": "nextPageToken,newStartPageToken,changes(fileId,file(id,name,mimeType,size,modifiedTime,createdTime,owners,parents,webViewLink,trashed))",
            }

            changes_response = await loop.run_in_executor(
                None,
                lambda: service.changes().list(**request_params).execute(),
            )

            changes = changes_response.get("changes", [])

            # Process each change
            for change in changes:
                await self._process_polling_change(trigger_id, change, polling_state)

            # Update polling state
            new_page_token = changes_response.get(
                "newStartPageToken"
            ) or changes_response.get("nextPageToken")
            if new_page_token:
                polling_state["page_token"] = new_page_token

            polling_state["last_poll"] = datetime.now(timezone.utc)

        except Exception as e:
            logger.error(
                f"Error checking drive changes for trigger {trigger_id}: {str(e)}"
            )

    async def _process_polling_change(
        self, trigger_id: UUID, change: Dict[str, Any], polling_state: Dict[str, Any]
    ):
        """
        Process a single change from polling.

        Args:
            trigger_id: Trigger ID
            change: Change data from Google Drive API
            polling_state: Current polling state
        """
        try:
            file_data = change.get("file")
            if not file_data:
                return

            # Determine event type
            if file_data.get("trashed"):
                event_type = TriggerEventType.DELETED
            else:
                # For polling, we can't easily distinguish between created and updated
                # Default to updated for existing files
                event_type = TriggerEventType.UPDATED

            # Check if event type is configured
            if event_type not in polling_state["event_types"]:
                return

            # Check folder filtering
            folder_ids = polling_state.get("folder_ids", [])
            if folder_ids and not self._is_file_in_folders(file_data, folder_ids):
                return

            # Create trigger event
            trigger_event = TriggerEvent(
                trigger_id=trigger_id,
                workflow_id=polling_state["workflow_id"],
                event_type=event_type,
                event_data={
                    "file_id": file_data.get("id"),
                    "file_name": file_data.get("name"),
                    "file_type": file_data.get("mimeType"),
                    "file_size": file_data.get("size"),
                    "modified_time": file_data.get("modifiedTime"),
                    "created_time": file_data.get("createdTime"),
                    "owners": file_data.get("owners", []),
                    "parents": file_data.get("parents", []),
                    "web_view_link": file_data.get("webViewLink"),
                    "credential_type": polling_state["credential_type"],
                    "user_id": polling_state.get("user_id"),
                    "service_account_file": polling_state.get("service_account_file"),
                    "source": "polling",
                },
                timestamp=datetime.now(timezone.utc),
                raw_event=change,
            )

            # Send event to trigger manager
            await self._send_event_to_trigger_manager(trigger_event)

            logger.info(
                f"Processed polling change for trigger {trigger_id}",
                event_type=event_type,
                file_name=file_data.get("name"),
                file_id=file_data.get("id"),
            )

        except Exception as e:
            logger.error(f"Error processing polling change: {str(e)}")

    async def _send_event_to_trigger_manager(self, trigger_event: TriggerEvent):
        """
        Send trigger event to the trigger manager.

        Args:
            trigger_event: Trigger event to send
        """
        try:
            # Store event in database
            async for session in get_async_session():
                drive_event = DriveEvents(
                    trigger_id=trigger_event.trigger_id,
                    event_type=trigger_event.event_type.value,
                    event_data=trigger_event.event_data,
                    timestamp=trigger_event.timestamp,
                    processed=False,
                )
                session.add(drive_event)
                await session.commit()
                break

            logger.info(
                f"Stored drive event for trigger {trigger_event.trigger_id}",
                event_type=trigger_event.event_type,
            )

        except Exception as e:
            logger.error(f"Error sending event to trigger manager: {str(e)}")

    async def cleanup_trigger(self, trigger_id: UUID) -> bool:
        """
        Clean up resources for a Google Drive unified trigger.

        Args:
            trigger_id: ID of the trigger to clean up

        Returns:
            bool: True if cleanup was successful, False otherwise
        """
        try:
            logger.info(f"🧹 Cleaning up Google Drive unified trigger {trigger_id}")

            success = True

            # Clean up webhook subscription
            if trigger_id in self._webhook_subscriptions:
                subscription_info = self._webhook_subscriptions[trigger_id]
                channel_id = subscription_info.get("subscription_id")

                if channel_id and channel_id in self._active_channels:
                    # Stop the webhook subscription
                    try:
                        # Get credentials for cleanup
                        config = {
                            "user_id": subscription_info.get("user_id"),
                            "service_account_file": subscription_info.get(
                                "service_account_file"
                            ),
                        }
                        credentials = await self._get_credentials(config)

                        if credentials:
                            service = await self._create_drive_service(credentials)
                            if service:
                                # Stop the channel
                                channel_info = self._active_channels[channel_id]
                                resource_id = channel_info.get("resource_id")

                                if resource_id:
                                    loop = asyncio.get_event_loop()
                                    await loop.run_in_executor(
                                        None,
                                        lambda: service.channels()
                                        .stop(
                                            body={
                                                "id": channel_id,
                                                "resourceId": resource_id,
                                            }
                                        )
                                        .execute(),
                                    )
                                    logger.info(f"Stopped webhook channel {channel_id}")

                    except Exception as e:
                        logger.error(f"Error stopping webhook channel: {str(e)}")
                        success = False

                    # Remove from active channels
                    if channel_id in self._active_channels:
                        del self._active_channels[channel_id]

                # Remove subscription info
                del self._webhook_subscriptions[trigger_id]

            # Clean up polling task
            if trigger_id in self._polling_tasks:
                polling_task = self._polling_tasks[trigger_id]
                if not polling_task.done():
                    polling_task.cancel()
                    try:
                        await polling_task
                    except asyncio.CancelledError:
                        pass
                del self._polling_tasks[trigger_id]

            # Clean up polling state
            if trigger_id in self._polling_states:
                del self._polling_states[trigger_id]

            logger.info(f"✅ Successfully cleaned up trigger {trigger_id}")
            return success

        except Exception as e:
            logger.error(f"Failed to cleanup trigger {trigger_id}", error=str(e))
            return False

    async def get_health_status(self) -> AdapterHealthStatus:
        """
        Get the health status of the Google Drive unified adapter.

        Returns:
            AdapterHealthStatus: Current health status
        """
        try:
            # Check webhook subscriptions
            active_webhooks = len(self._webhook_subscriptions)
            expired_webhooks = 0
            current_time = datetime.now()

            for subscription_info in self._webhook_subscriptions.values():
                if subscription_info.get("expires_at", current_time) < current_time:
                    expired_webhooks += 1

            # Check polling tasks
            active_polling = len(self._polling_tasks)
            failed_polling = 0

            for task in self._polling_tasks.values():
                if task.done() and task.exception():
                    failed_polling += 1

            # Determine overall health
            is_healthy = True
            issues = []

            if expired_webhooks > 0:
                issues.append(f"{expired_webhooks} expired webhook subscriptions")
                is_healthy = False

            if failed_polling > 0:
                issues.append(f"{failed_polling} failed polling tasks")
                is_healthy = False

            # Check credential health
            try:
                # Test user credentials
                user_creds_healthy = True
                for user_id, credentials in self._user_credentials.items():
                    if not credentials.valid:
                        user_creds_healthy = False
                        issues.append(f"Invalid user credentials for {user_id}")

                # Test service account credentials
                if (
                    self._service_account_credentials
                    and not self._service_account_credentials.valid
                ):
                    issues.append("Invalid service account credentials")
                    is_healthy = False

            except Exception as cred_error:
                issues.append(f"Credential check failed: {str(cred_error)}")
                is_healthy = False

            return AdapterHealthStatus(
                adapter_name=self.adapter_name,
                is_healthy=is_healthy,
                last_check=datetime.now(timezone.utc),
                details={
                    "active_webhooks": active_webhooks,
                    "expired_webhooks": expired_webhooks,
                    "active_polling": active_polling,
                    "failed_polling": failed_polling,
                    "active_channels": len(self._active_channels),
                    "cached_user_credentials": len(self._user_credentials),
                    "has_service_account_credentials": bool(
                        self._service_account_credentials
                    ),
                    "issues": issues,
                },
            )

        except Exception as e:
            return AdapterHealthStatus(
                adapter_name=self.adapter_name,
                is_healthy=False,
                last_check=datetime.now(timezone.utc),
                details={"error": str(e)},
            )

    async def stop(self):
        """Stop the Google Drive unified adapter and clean up resources."""
        try:
            logger.info("🛑 Stopping Google Drive unified adapter")

            # Cancel all polling tasks
            for trigger_id, task in list(self._polling_tasks.items()):
                if not task.done():
                    task.cancel()
                    try:
                        await task
                    except asyncio.CancelledError:
                        pass

            # Clear all state
            self._webhook_subscriptions.clear()
            self._polling_tasks.clear()
            self._polling_states.clear()
            self._active_channels.clear()
            self._user_credentials.clear()
            self._service_account_credentials = None
            self._services.clear()

            logger.info("✅ Google Drive unified adapter stopped successfully")

        except Exception as e:
            logger.error(f"Error stopping Google Drive unified adapter: {str(e)}")
