"""
Google Drive User Adapter for the Trigger Service.

This module implements the Google Drive user adapter that monitors
drive events via webhooks for individual users using OAuth credentials.
"""

import asyncio
import json
import os
from datetime import datetime, timedelta, timezone
from typing import Any, Dict, List, Optional, Set
from uuid import UUID

import httpx
from google.auth.transport.requests import Request
from google.oauth2.credentials import Credentials as UserCredentials
from googleapiclient.discovery import build
from googleapiclient.errors import HttpError
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, update

from src.adapters.base import (
    BaseTriggerAdapter,
    TriggerEvent,
    TriggerEventType,
    TriggerConfiguration,
    AdapterHealthStatus,
)
from src.database.connection import get_async_session
from src.database.models import (
    DriveEvents,
    Trigger,
)
from src.utils.config import get_settings
from src.utils.logger import get_logger
from src.utils.retry import <PERSON><PERSON><PERSON><PERSON><PERSON>, RetryableError

logger = get_logger(__name__)


class GoogleDriveUserError(Exception):
    """Base exception for Google Drive user adapter errors."""

    pass


class GoogleDriveUserAuthError(GoogleDriveUserError):
    """Authentication-related errors."""

    pass


class GoogleDriveUserAPIError(GoogleDriveUserError, RetryableError):
    """API-related errors that should trigger retry."""

    pass


class GoogleDriveUserAdapter(BaseTriggerAdapter):
    """
    Google Drive user adapter for individual user monitoring.

    This adapter integrates with Google Drive API to:
    - Set up webhook subscriptions for drive events using OAuth user credentials
    - Process incoming webhook events
    - Transform drive events into workflow executions
    - Manage subscription lifecycle and renewal
    """

    _instance = None

    def __new__(cls):
        """Ensure singleton pattern for GoogleDriveUserAdapter."""
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance

    @classmethod
    def get_instance(cls):
        """Get the singleton instance of GoogleDriveUserAdapter."""
        if cls._instance is None:
            cls._instance = cls()
        return cls._instance

    def __init__(self):
        """Initialize the Google Drive user adapter."""
        # Only initialize once (singleton pattern)
        if hasattr(self, "_initialized"):
            return

        super().__init__("google_drive")
        self.settings = get_settings()

        self.retry_handler = RetryHandler(
            retryable_exceptions=[
                GoogleDriveUserAPIError,
                HttpError,
                ConnectionError,
            ]
        )

        # Multi-user webhook and polling management
        self._webhook_subscriptions: Dict[UUID, Dict[str, Any]] = {}
        self._polling_tasks: Dict[UUID, asyncio.Task] = {}
        self._polling_states: Dict[UUID, Dict[str, Any]] = {}

        # User credential management
        self._user_credentials: Dict[str, UserCredentials] = {}
        self._user_services: Dict[str, Any] = {}

        # Channel management
        self._active_channels: Dict[str, Dict[str, Any]] = {}

        # Mark as initialized
        self._initialized = True

        logger.info(
            "Google Drive user adapter initialized",
            active_channels_count=len(self._active_channels),
        )

    @property
    def supported_event_types(self) -> Set[TriggerEventType]:
        """
        Get the set of event types supported by this adapter.

        Returns:
            Set[TriggerEventType]: Supported event types
        """
        return {
            TriggerEventType.CREATED,
            TriggerEventType.UPDATED,
            TriggerEventType.DELETED,
            TriggerEventType.MOVED,
        }

    async def validate_config(self, config: Dict[str, Any]) -> bool:
        """
        Validate Google Drive user adapter configuration.

        Args:
            config: Configuration to validate

        Returns:
            bool: True if configuration is valid, False otherwise
        """
        try:
            # Note: user_id is now provided via TriggerConfiguration.user_id, not in config
            # No required fields validation needed for user_id in config anymore

            # Optional fields validation
            if "folder_ids" in config:
                folder_ids = config["folder_ids"]
                if not isinstance(folder_ids, list):
                    logger.error("folder_ids must be a list")
                    return False

            if "file_types" in config:
                file_types = config["file_types"]
                if not isinstance(file_types, list):
                    logger.error("file_types must be a list")
                    return False

            # Validate webhook_ttl if provided
            if "webhook_ttl" in config:
                webhook_ttl = config["webhook_ttl"]
                if not isinstance(webhook_ttl, int) or webhook_ttl <= 0:
                    logger.error("webhook_ttl must be a positive integer")
                    return False

            logger.debug("Configuration validation passed")
            return True

        except Exception as e:
            logger.error(f"Error validating configuration: {str(e)}")
            return False

    async def setup_trigger(
        self, trigger_config: TriggerConfiguration, session=None
    ) -> bool:
        """
        Set up a new Google Drive user trigger with webhook subscription or polling fallback.

        Args:
            trigger_config: Complete trigger configuration
            session: Optional database session

        Returns:
            bool: True if setup was successful, False otherwise
        """
        try:
            logger.info(
                "🔧 Setting up Google Drive user trigger",
                trigger_id=trigger_config.trigger_id,
                user_id=trigger_config.user_id,  # Use user_id from TriggerConfiguration
                folder_ids=trigger_config.config.get("folder_ids"),
                event_types=trigger_config.event_types,
                use_polling=trigger_config.config.get("use_polling", False),
            )

            # Get user credentials using user_id from TriggerConfiguration
            credentials = await self._get_user_credentials(trigger_config.user_id)
            if not credentials:
                logger.error(
                    "❌ Failed to get Google Drive user credentials",
                    user_id=trigger_config.user_id,
                    trigger_id=trigger_config.trigger_id,
                )
                return False

            # Create Google Drive service
            service = await self._create_drive_service(credentials)
            if not service:
                logger.error("Failed to create Google Drive service")
                return False

            # Check if polling mode is explicitly requested or if webhook setup fails
            use_polling = trigger_config.config.get("use_polling", False)

            if not use_polling:
                # Try webhook setup first
                subscription_id = await self._create_webhook_subscription(
                    service, trigger_config, session
                )
                if subscription_id:
                    # Get the resource_id from the active channel
                    channel_info = self._active_channels.get(subscription_id)
                    resource_id = (
                        channel_info.get("resource_id")
                        if channel_info
                        else subscription_id
                    )

                    # Store subscription information
                    self._webhook_subscriptions[trigger_config.trigger_id] = {
                        "subscription_id": subscription_id,
                        "user_id": trigger_config.user_id,  # Use user_id from TriggerConfiguration
                        "folder_ids": trigger_config.config.get("folder_ids", []),
                        "created_at": datetime.now(),
                        "expires_at": datetime.now()
                        + timedelta(
                            seconds=trigger_config.config.get("webhook_ttl", 3600)
                        ),
                        "resource_id": resource_id,
                    }

                    logger.info(
                        "✅ Google Drive user webhook trigger setup successful",
                        trigger_id=trigger_config.trigger_id,
                        user_id=trigger_config.user_id,  # Use user_id from TriggerConfiguration
                        subscription_id=subscription_id,
                        folder_ids=trigger_config.config.get("folder_ids"),
                    )
                    return True
                else:
                    logger.warning(
                        "⚠️ Webhook setup failed, falling back to polling",
                        trigger_id=trigger_config.trigger_id,
                        user_id=trigger_config.user_id,  # Use user_id from TriggerConfiguration
                    )
                    use_polling = True

            if use_polling:
                # Set up polling mode
                success = await self._setup_polling_trigger(trigger_config, service)
                if success:
                    logger.info(
                        "✅ Google Drive user polling trigger setup successful",
                        trigger_id=trigger_config.trigger_id,
                        user_id=trigger_config.user_id,  # Use user_id from TriggerConfiguration
                        poll_interval=trigger_config.config.get(
                            "poll_interval_seconds", 300
                        ),
                        folder_ids=trigger_config.config.get("folder_ids"),
                    )
                    return True

            logger.error(
                "Failed to set up trigger with both webhook and polling methods"
            )
            return False

        except Exception as e:
            logger.error(
                f"Failed to setup Google Drive user trigger {trigger_config.trigger_id}",
                error=str(e),
            )
            return False

    async def remove_trigger(self, trigger_id: UUID) -> bool:
        """
        Remove a Google Drive user trigger and its webhook subscription or polling task.

        Args:
            trigger_id: Unique identifier for the trigger to remove

        Returns:
            bool: True if removal was successful, False otherwise
        """
        try:
            logger.info("Removing Google Drive user trigger", trigger_id=trigger_id)

            # Stop polling task if exists
            if trigger_id in self._polling_tasks:
                task = self._polling_tasks[trigger_id]
                if not task.done():
                    task.cancel()
                    try:
                        await task
                    except asyncio.CancelledError:
                        pass
                del self._polling_tasks[trigger_id]
                logger.info("Stopped polling task for trigger", trigger_id=trigger_id)

            # Remove polling state if exists
            if trigger_id in self._polling_states:
                del self._polling_states[trigger_id]

            # DATABASE CLEANUP: Get trigger info from database and clean up webhook
            try:
                trigger_record = None
                async for session in get_async_session():
                    result = await session.execute(
                        select(Trigger).where(Trigger.id == trigger_id)
                    )
                    trigger_record = result.scalar_one_or_none()
                    break

                if trigger_record and trigger_record.channel_id:
                    # Get user credentials
                    user_id = trigger_record.trigger_config.get("user_id")
                    if user_id:
                        credentials = await self._get_user_credentials(user_id)
                        if credentials:
                            # Create Google Drive service
                            service = await self._create_drive_service(credentials)
                            if service:
                                # Remove webhook subscription using channel_id from database
                                await self._remove_webhook_subscription(
                                    service,
                                    trigger_record.channel_id,
                                    None,
                                )

                    # Clear channel_id from database
                    async for session in get_async_session():
                        await session.execute(
                            update(Trigger)
                            .where(Trigger.id == trigger_id)
                            .values(channel_id=None)
                        )
                        await session.commit()
                        break

                    logger.info(
                        "✅ DATABASE CLEANUP: Removed webhook subscription and cleared channel_id",
                        trigger_id=trigger_id,
                        channel_id=trigger_record.channel_id,
                    )

                    # Remove from in-memory cache if exists
                    if trigger_record.channel_id in self._active_channels:
                        del self._active_channels[trigger_record.channel_id]

            except Exception as db_error:
                logger.error(
                    "❌ DATABASE CLEANUP: Failed to clean up webhook from database",
                    error=str(db_error),
                    trigger_id=trigger_id,
                )

            # Get webhook subscription info (fallback for old in-memory storage)
            subscription_info = self._webhook_subscriptions.get(trigger_id)
            if subscription_info:
                # Get user credentials
                user_id = subscription_info["user_id"]
                credentials = await self._get_user_credentials(user_id)
                if credentials:
                    # Create Google Drive service
                    service = await self._create_drive_service(credentials)
                    if service:
                        # Remove webhook subscription
                        await self._remove_webhook_subscription(
                            service,
                            subscription_info["subscription_id"],
                            subscription_info.get("resource_id"),
                        )

                # Remove from local storage
                del self._webhook_subscriptions[trigger_id]
                logger.info(
                    "Removed webhook subscription for trigger", trigger_id=trigger_id
                )

            logger.info(
                "Successfully removed Google Drive user trigger",
                trigger_id=trigger_id,
            )
            return True

        except Exception as e:
            logger.error(
                f"Failed to remove Google Drive user trigger {trigger_id}",
                error=str(e),
            )
            return False

    async def process_event(self, raw_event: Dict[str, Any]) -> Optional[TriggerEvent]:
        """
        Process a raw Google Drive webhook event.

        Args:
            raw_event: Raw event data from Google Drive webhook

        Returns:
            TriggerEvent: Standardized event data, or None if event should be ignored
        """
        try:
            logger.info(
                "📨 Processing Google Drive user webhook event",
                event_type=raw_event.get("type"),
                has_headers=bool(raw_event.get("headers")),
                has_webhook_headers=bool(raw_event.get("webhook_headers")),
                channel_id=raw_event.get("webhook_headers", {}).get(
                    "x-goog-channel-id"
                ),
                resource_state=raw_event.get("webhook_headers", {}).get(
                    "x-goog-resource-state"
                ),
            )

            # Handle verification events (sync messages)
            if raw_event.get("type") == "verification":
                logger.info("Received webhook verification/sync event")
                return None

            # Extract webhook headers for validation
            headers = raw_event.get("webhook_headers", {})

            if not headers:
                logger.warning("No webhook_headers found in event")
                return None

            # Validate webhook authenticity
            if not self._validate_webhook(headers, raw_event):
                logger.warning("Invalid webhook received, ignoring")
                return None

            # Get channel information to identify the user and trigger
            channel_id = headers.get("x-goog-channel-id")

            if not channel_id:
                logger.warning("Missing channel ID in webhook headers")
                return None

            try:
                trigger_info = None
                async for session in get_async_session():
                    # Query database for trigger with this channel_id
                    result = await session.execute(
                        select(Trigger).where(Trigger.channel_id == channel_id)
                    )
                    trigger_record = result.scalar_one_or_none()

                    if trigger_record:
                        trigger_info = {
                            "trigger_id": trigger_record.id,
                            "user_id": trigger_record.trigger_config.get("user_id"),
                            "folder_ids": trigger_record.trigger_config.get(
                                "folder_ids", []
                            ),
                            "event_types": trigger_record.event_types,
                            "is_active": trigger_record.is_active,
                            "workflow_id": trigger_record.workflow_id,
                        }
                    break

                if not trigger_info:
                    logger.warning(
                        "No trigger found for channel_id",
                        channel_id=channel_id,
                    )
                    return None

                if not trigger_info["is_active"]:
                    logger.warning(
                        "Trigger is inactive for channel_id",
                        trigger_id=trigger_info["trigger_id"],
                        channel_id=channel_id,
                    )
                    return None

                # Extract trigger information from database result
                user_id = trigger_info["user_id"]
                trigger_id = trigger_info["trigger_id"]
                folder_ids = trigger_info["folder_ids"]
                configured_event_types = trigger_info["event_types"]
                workflow_id = trigger_info["workflow_id"]

            except Exception as db_error:
                logger.error(
                    "Failed to fetch trigger info from database",
                    error=str(db_error),
                    channel_id=channel_id,
                )
                return None

            logger.info(
                "Processing webhook for user",
                user_id=user_id,
                trigger_id=trigger_id,
                channel_id=channel_id,
                folder_ids=folder_ids,
                resource_state=headers.get("x-goog-resource-state"),
            )

            # Process the drive event notification and return TriggerEvent
            trigger_event = await self._process_drive_notification(
                user_id,
                folder_ids,
                trigger_id,
                workflow_id,
                headers,
                raw_event,
                configured_event_types,
            )

            return trigger_event

        except Exception as e:
            logger.error("Failed to process Google Drive user event", error=str(e))
            return None

    async def _process_drive_notification(
        self,
        user_id: str,
        folder_ids: List[str],
        trigger_id: UUID,
        workflow_id: UUID,
        headers: Dict[str, str],
        raw_event: Dict[str, Any],
        configured_event_types: List[str],
    ) -> Optional[TriggerEvent]:
        """
        Process drive notification and return TriggerEvent for workflow execution.

        Args:
            user_id: User ID
            folder_ids: List of folder IDs being monitored
            trigger_id: Trigger ID
            workflow_id: Workflow ID to execute
            headers: Webhook headers
            raw_event: Raw webhook event
            configured_event_types: List of configured event types

        Returns:
            TriggerEvent: Standardized trigger event for workflow execution
        """
        try:
            # Get channel info
            channel_id = headers.get("x-goog-channel-id")
            resource_id = headers.get("x-goog-resource-id")

            logger.debug(
                "Processing Google Drive user webhook notification",
                user_id=user_id,
                trigger_id=trigger_id,
                channel_id=channel_id,
                resource_id=resource_id,
                folder_ids=folder_ids,
                resource_state=headers.get("x-goog-resource-state"),
            )

            # Get user credentials and service
            credentials = await self._get_user_credentials(user_id)
            if not credentials:
                logger.error(
                    "No credentials found for user",
                    user_id=user_id,
                )
                return None

            service = await self._create_drive_service(credentials)
            if not service:
                logger.error(
                    "Failed to create service for user",
                    user_id=user_id,
                )
                return None

            # Fetch recent drive events
            events = await self._fetch_recent_drive_events(service, folder_ids, user_id)

            if not events:
                logger.debug("No recent events found", user_id=user_id)
                return None

            logger.debug(
                "Found recent events",
                user_id=user_id,
                event_count=len(events),
            )

            # Process the most recent event for workflow execution
            if events:
                event = events[0]  # Take the most recent event

                # Determine event type based on webhook resource state and event data
                event_type = self._determine_event_type(event, headers)

                # Check if this event type is configured for this trigger
                if event_type and event_type.value not in configured_event_types:
                    logger.debug(
                        "Event type not configured for trigger",
                        event_type=event_type.value,
                        configured_types=configured_event_types,
                        trigger_id=trigger_id,
                    )
                    return None

                # Store complete event data in database
                await self._store_drive_event_in_database(
                    channel_id=channel_id,
                    resource_id=resource_id,
                    user_id=user_id,
                    event=event,
                    event_type=event_type.value if event_type else "updated",
                )

                logger.info(
                    "Processing drive event for workflow execution",
                    event_type=event_type.value if event_type else "updated",
                    user_id=user_id,
                    file_name=event.get("name", "Unknown"),
                    file_id=event.get("id"),
                    trigger_id=trigger_id,
                    workflow_id=workflow_id,
                )

                # Create TriggerEvent for workflow execution
                trigger_event = TriggerEvent(
                    trigger_id=trigger_id,
                    workflow_id=workflow_id,
                    event_type=event_type or TriggerEventType.UPDATED,
                    event_data=event,
                    metadata={
                        "user_id": user_id,
                        "channel_id": channel_id,
                        "resource_id": resource_id,
                        "folder_ids": folder_ids,
                        "timestamp": datetime.now(timezone.utc).isoformat(),
                        "source": "google_drive_user_webhook",
                    },
                )

                return trigger_event

            return None

        except Exception as e:
            logger.error(
                "Failed to process drive notification",
                error=str(e),
                user_id=user_id,
                trigger_id=trigger_id,
            )
            return None

    async def _fetch_recent_drive_events(
        self, service, folder_ids: List[str], user_id: str
    ) -> List[Dict[str, Any]]:
        """
        Fetch recent drive events from Google Drive API.

        Args:
            service: Google Drive service instance
            folder_ids: List of folder IDs to monitor
            user_id: User ID for logging

        Returns:
            List[Dict[str, Any]]: List of drive events
        """
        try:
            # Get the most recently modified files (webhook-triggered, no need for time tracking)
            now = datetime.now(timezone.utc)

            # Short time window to catch very recent changes (webhooks are real-time)
            modified_time = (
                (now - timedelta(minutes=1)).isoformat().replace("+00:00", "Z")
            )

            logger.debug(
                "Fetching recent events",
                user_id=user_id,
                modified_time=modified_time,
            )

            # Build query for recent files
            query_parts = [f"modifiedTime > '{modified_time}'"]

            # Add folder restrictions if specified
            if folder_ids:
                folder_queries = [
                    f"'{folder_id}' in parents" for folder_id in folder_ids
                ]
                query_parts.append(f"({' or '.join(folder_queries)})")

            query = " and ".join(query_parts)

            # Build query parameters
            query_params = {
                "q": query,
                "pageSize": 10,  # Only get the most recent files
                "orderBy": "modifiedTime desc",  # Order by last modification time
                "fields": "files(id,name,mimeType,parents,modifiedTime,createdTime,size,webViewLink,owners)",
            }

            loop = asyncio.get_event_loop()
            files_result = await loop.run_in_executor(
                None,
                lambda: service.files().list(**query_params).execute(),
            )

            files = files_result.get("files", [])

            if not files:
                logger.debug("No recent files found", user_id=user_id)
                return []

            logger.debug(
                "Fetched recent files",
                user_id=user_id,
                file_count=len(files),
            )

            return files

        except Exception as e:
            logger.error(
                "Failed to fetch recent drive events",
                error=str(e),
                user_id=user_id,
            )
            return []

    async def _store_drive_event_in_database(
        self,
        channel_id: str,
        resource_id: str,
        user_id: str,
        event: Dict[str, Any],
        event_type: str,
    ) -> bool:
        """
        Store complete drive event data in the database.

        Args:
            channel_id: Google Drive webhook channel ID
            resource_id: Google Drive resource ID
            user_id: User ID
            event: Complete event data from Google Drive API
            event_type: Type of event (created, updated, deleted, moved)

        Returns:
            bool: True if storage was successful
        """
        try:
            file_id = event.get("id")
            if not file_id:
                logger.warning("Event missing ID, cannot store in database")
                return False

            # Parse Google Drive timestamps
            google_created_at = None
            google_modified_at = None

            if event.get("createdTime"):
                try:
                    google_created_at = datetime.fromisoformat(
                        event["createdTime"].replace("Z", "+00:00")
                    )
                except Exception as date_error:
                    logger.warning(
                        f"Failed to parse createdTime timestamp: {date_error}"
                    )

            if event.get("modifiedTime"):
                try:
                    google_modified_at = datetime.fromisoformat(
                        event["modifiedTime"].replace("Z", "+00:00")
                    )
                except Exception as date_error:
                    logger.warning(
                        f"Failed to parse modifiedTime timestamp: {date_error}"
                    )

            # Extract folder ID from parents
            folder_id = None
            if event.get("parents"):
                folder_id = event["parents"][0]  # Use first parent as folder ID

            # Create drive event record
            drive_event = DriveEvents(
                channel_id=channel_id,
                resource_id=resource_id,
                user_id=user_id,
                file_id=file_id,
                folder_id=folder_id,
                event_data=event,
                event_type=event_type,
                trigger_type="user",
                google_created_at=google_created_at,
                google_modified_at=google_modified_at,
            )

            # Store in database
            async for session in get_async_session():
                # Check if event already exists (for upsert behavior)
                existing_event = await session.execute(
                    select(DriveEvents).where(
                        and_(
                            DriveEvents.user_id == user_id,
                            DriveEvents.file_id == file_id,
                            DriveEvents.event_type == event_type,
                        )
                    )
                )
                existing = existing_event.scalar_one_or_none()

                if existing:
                    # Update existing event
                    existing.event_data = event
                    existing.google_modified_at = google_modified_at
                    existing.channel_id = channel_id
                    existing.resource_id = resource_id
                    existing.folder_id = folder_id
                    logger.debug(f"Updated existing drive event {file_id} in database")
                else:
                    # Create new event
                    session.add(drive_event)
                    logger.debug("Stored new drive event in database", file_id=file_id)

                await session.commit()
                break

            logger.debug(
                "Stored drive event in database",
                file_id=file_id,
                user_id=user_id,
                event_type=event_type,
                file_name=event.get("name", "Unknown"),
            )
            return True

        except Exception as e:
            logger.error(
                "Failed to store drive event in database",
                error=str(e),
                file_id=event.get("id", "unknown"),
                user_id=user_id,
            )
            return False

    async def _get_user_credentials(self, user_id: str) -> Optional[UserCredentials]:
        """
        Get Google Drive user credentials for a user.

        Args:
            user_id: ID of the user

        Returns:
            UserCredentials: Google user credentials, or None if not found
        """
        try:
            # Check if we have cached credentials for this user
            if user_id in self._user_credentials:
                credentials = self._user_credentials[user_id]

                # Check if credentials are still valid
                if credentials.valid:
                    return credentials

                # Try to refresh if expired
                if credentials.expired and credentials.refresh_token:
                    logger.info(
                        "Refreshing cached credentials for user", user_id=user_id
                    )
                    try:
                        loop = asyncio.get_event_loop()
                        await loop.run_in_executor(None, credentials.refresh, Request())
                        logger.info(
                            f"Successfully refreshed credentials for user {user_id}"
                        )
                        return credentials
                    except Exception as refresh_error:
                        logger.error(
                            f"Failed to refresh cached credentials: {refresh_error}"
                        )
                        # Remove invalid credentials from cache
                        del self._user_credentials[user_id]

            # Load fresh credentials from storage
            credentials = await self._load_user_credentials_from_storage(user_id)
            if credentials:
                # Cache the credentials for future use
                self._user_credentials[user_id] = credentials
                logger.info("Cached credentials for user", user_id=user_id)
                return credentials

            return None

        except Exception as e:
            logger.error(f"Failed to get credentials for user {user_id}", error=str(e))
            return None

    async def _load_user_credentials_from_storage(
        self, user_id: str
    ) -> Optional[UserCredentials]:
        """
        Load user credentials from auth service.
        Updated to use the external auth service for OAuth credentials.

        Args:
            user_id: ID of the user

        Returns:
            UserCredentials: Google user credentials, or None if not found
        """
        try:
            from src.utils.auth_client import AuthClient, AuthServiceConnectionError

            logger.info(
                f"Loading Google Drive user credentials for user {user_id} from auth service"
            )

            # Try to fetch credentials from auth service
            try:
                # Create auth client instance and use async context manager
                auth_client = AuthClient()
                async with auth_client:
                    # Fetch OAuth credentials from auth service for Google Drive
                    oauth_credentials = await auth_client.get_oauth_credentials(
                        user_id, tool_name="google_drive", provider="google"
                    )

                    if not oauth_credentials:
                        logger.warning(
                            f"No OAuth credentials found for user {user_id} in auth service"
                        )
                        return None

                    logger.debug(
                        "OAuth credentials received",
                        oauth_credentials=oauth_credentials,
                    )

                    # Extract Google Drive credentials from OAuth response
                    if not oauth_credentials.get("success"):
                        logger.warning(
                            f"OAuth credentials request was not successful for user {user_id}"
                        )
                        return None

                    # Validate required fields
                    required_fields = ["access_token"]
                    for field in required_fields:
                        if field not in oauth_credentials:
                            logger.error(
                                f"Missing required OAuth field '{field}' for user {user_id}"
                            )
                            return None

                    # Extract scopes from the response (convert space-separated string to list)
                    scope_string = oauth_credentials.get("scope", "")
                    scopes = (
                        scope_string.split()
                        if scope_string
                        else [
                            "https://www.googleapis.com/auth/drive",
                            "https://www.googleapis.com/auth/drive.file",
                            "https://www.googleapis.com/auth/drive.metadata",
                        ]
                    )

                    # Create Google OAuth2 credentials object
                    credentials = UserCredentials(
                        token=oauth_credentials.get("access_token"),
                        refresh_token=oauth_credentials.get("refresh_token"),
                        token_uri="https://oauth2.googleapis.com/token",
                        client_id=(
                            self.settings.google_client_id
                            if hasattr(self.settings, "google_client_id")
                            else None
                        ),
                        client_secret=(
                            self.settings.google_client_secret
                            if hasattr(self.settings, "google_client_secret")
                            else None
                        ),
                        scopes=scopes,
                    )

                    # Check if token needs refresh
                    if credentials.expired and credentials.refresh_token:
                        logger.info(
                            "Refreshing expired token for user", user_id=user_id
                        )
                        try:
                            # Use asyncio to refresh token without blocking
                            loop = asyncio.get_event_loop()
                            await loop.run_in_executor(
                                None, credentials.refresh, Request()
                            )

                            # TODO: Update refreshed token back to auth service
                            # This would require an update endpoint in the auth service
                            logger.info("Token refreshed for user", user_id=user_id)
                            logger.warning(
                                f"Refreshed token not saved back to auth service - implement update endpoint"
                            )

                        except Exception as refresh_error:
                            logger.error(
                                f"Failed to refresh token for user {user_id}: {refresh_error}"
                            )
                            return None
                    elif credentials.expired:
                        logger.error(
                            f"Token expired and no refresh token available for user {user_id}"
                        )
                        return None

                    logger.info(
                        f"Successfully loaded credentials for user {user_id} from auth service"
                    )
                    return credentials

            except AuthServiceConnectionError as conn_error:
                logger.warning(
                    f"Auth service connection failed for user {user_id}: {conn_error}."
                )
                return None
            except Exception as auth_error:
                logger.error(f"Auth service error for user {user_id}: {auth_error}.")
                return None

        except Exception as e:
            logger.error(
                f"Failed to load credentials for user {user_id} from auth service",
                error=str(e),
            )
            return None

    async def _create_drive_service(self, credentials: UserCredentials):
        """
        Create Google Drive API service instance.

        Args:
            credentials: Google user credentials

        Returns:
            Google Drive service instance, or None if failed
        """
        try:
            service = build("drive", "v3", credentials=credentials)
            return service

        except Exception as e:
            logger.error(f"Failed to create Google Drive service", error=str(e))
            return None

    async def _create_webhook_subscription(
        self, service, trigger_config: TriggerConfiguration, session=None
    ) -> Optional[str]:
        """
        Create a webhook subscription for drive events.

        Args:
            service: Google Drive service instance
            trigger_config: Trigger configuration
            session: Optional database session

        Returns:
            str: Subscription ID if successful, None otherwise
        """
        try:
            user_id = trigger_config.user_id  # Use user_id from TriggerConfiguration
            folder_ids = trigger_config.config.get("folder_ids", [])
            webhook_ttl = trigger_config.config.get("webhook_ttl", 604800)  # 7 days

            # Check if webhook URL is configured
            if (
                not hasattr(self.settings, "google_drive_webhook_url")
                or not self.settings.google_drive_webhook_url
            ):
                logger.error("Google Drive webhook URL not configured")
                return None

            # Validate that webhook URL is HTTPS (required by Google Drive)
            webhook_url = self.settings.google_drive_webhook_url
            logger.info("Using webhook URL", webhook_url=webhook_url)

            if not webhook_url.startswith("https://"):
                logger.error(
                    f"Google Drive webhook URL must use HTTPS, got: {webhook_url}",
                    extra={
                        "help": "For development, use ngrok to create an HTTPS tunnel: 'ngrok http 8000'"
                    },
                )
                return None

            # Generate unique channel ID
            import uuid

            channel_id = f"drive-user-trigger-{trigger_config.trigger_id}-{str(uuid.uuid4())[:8]}"

            # Calculate expiration time
            expiration = int(
                (datetime.now(timezone.utc).timestamp() + webhook_ttl) * 1000
            )

            # Prepare webhook subscription request
            body = {
                "id": channel_id,
                "type": "web_hook",
                "address": webhook_url,
                "expiration": str(expiration),
                "params": {
                    "ttl": str(webhook_ttl),
                },
            }

            # Add token for verification if configured
            webhook_secret = getattr(self.settings, "google_drive_webhook_secret", None)
            if webhook_secret:
                body["token"] = webhook_secret

            # Create the subscription with timeout
            # For Drive, we watch changes to the entire drive or specific folders
            if folder_ids:
                # Watch specific folders
                for folder_id in folder_ids:
                    result = await asyncio.wait_for(
                        self._execute_watch_request(service, folder_id, body),
                        timeout=30.0,
                    )
                    if result:
                        subscription_id = result.get("id")
                        resource_id = result.get("resourceId")
                        break
            else:
                # Watch entire drive
                result = await asyncio.wait_for(
                    self._execute_watch_request(service, "root", body),
                    timeout=30.0,
                )

            if result:
                subscription_id = result.get("id")
                resource_id = result.get("resourceId")

                # Save channel_id to database
                try:
                    if session:
                        # Use the passed session
                        await session.execute(
                            update(Trigger)
                            .where(Trigger.id == trigger_config.trigger_id)
                            .values(channel_id=channel_id)
                        )
                    else:
                        # Fallback: create our own session
                        async for db_session in get_async_session():
                            await db_session.execute(
                                update(Trigger)
                                .where(Trigger.id == trigger_config.trigger_id)
                                .values(channel_id=channel_id)
                            )
                            await db_session.commit()
                            break

                    logger.info(
                        "Successfully saved channel_id to database",
                        channel_id=channel_id,
                        resource_id=resource_id,
                        trigger_id=trigger_config.trigger_id,
                        user_id=user_id,
                        folder_ids=folder_ids,
                    )

                    # Store channel info for management
                    self._active_channels[channel_id] = {
                        "resource_id": resource_id,
                        "expiration": expiration,
                        "trigger_id": trigger_config.trigger_id,
                        "user_id": user_id,
                        "folder_ids": folder_ids,
                        "event_types": trigger_config.event_types,
                        "created_at": datetime.now(timezone.utc).timestamp(),
                    }

                    return channel_id

                except Exception as db_error:
                    logger.error(
                        "Failed to save channel_id to database",
                        error=str(db_error),
                        channel_id=channel_id,
                        trigger_id=trigger_config.trigger_id,
                    )
                    # Fall back to in-memory storage if database fails
                    self._active_channels[channel_id] = {
                        "resource_id": resource_id,
                        "expiration": expiration,
                        "trigger_id": trigger_config.trigger_id,
                        "user_id": user_id,
                        "folder_ids": folder_ids,
                        "event_types": trigger_config.event_types,
                        "created_at": datetime.now(timezone.utc).timestamp(),
                    }
                    return channel_id

            return None

        except asyncio.TimeoutError:
            logger.error("Timeout creating webhook subscription")
            return None
        except Exception as e:
            logger.error(f"Failed to create webhook subscription", error=str(e))
            return None

    async def _execute_watch_request(
        self, service, resource_id: str, body: Dict[str, Any]
    ):
        """
        Execute the Google Drive watch request.

        Args:
            service: Google Drive service instance
            resource_id: Resource ID to watch (folder ID or "root")
            body: Request body for the watch request

        Returns:
            Dict: Response from the watch request
        """
        try:
            # Execute the watch request asynchronously
            loop = asyncio.get_event_loop()
            result = await loop.run_in_executor(
                None,
                lambda: service.files().watch(fileId=resource_id, body=body).execute(),
            )
            return result

        except HttpError as e:
            if e.resp.status in [500, 502, 503, 504]:
                # Retryable HTTP errors
                raise GoogleDriveUserAPIError(f"Google Drive API error: {str(e)}")
            else:
                # Non-retryable errors
                raise GoogleDriveUserError(f"Google Drive API error: {str(e)}")
        except Exception as e:
            raise GoogleDriveUserAPIError(f"Unexpected error: {str(e)}")

    async def _remove_webhook_subscription(
        self, service, channel_id: str, resource_id: Optional[str] = None
    ) -> bool:
        """
        Remove a webhook subscription.

        Args:
            service: Google Drive service instance
            channel_id: ID of the channel to stop
            resource_id: Resource ID of the subscription (optional)

        Returns:
            bool: True if removal was successful
        """
        try:
            # Stop the webhook subscription
            body = {
                "id": channel_id,
            }

            # Add resource ID if provided
            if resource_id:
                body["resourceId"] = resource_id

            loop = asyncio.get_event_loop()
            result = await loop.run_in_executor(
                None, lambda: service.channels().stop(body=body).execute()
            )

            logger.info(
                "Successfully removed webhook subscription", channel_id=channel_id
            )
            return True

        except HttpError as e:
            if e.resp.status == 404:
                # Subscription already removed or doesn't exist
                logger.info(
                    f"Webhook subscription {channel_id} not found (already removed)"
                )
                return True
            else:
                logger.error(
                    f"Failed to remove webhook subscription {channel_id}",
                    error=str(e),
                )
                return False
        except Exception as e:
            logger.error(
                f"Failed to remove webhook subscription {channel_id}", error=str(e)
            )
            return False

    def _validate_webhook(
        self, headers: Dict[str, str], event_data: Dict[str, Any]
    ) -> bool:
        """
        Validate Google Drive webhook authenticity.

        Args:
            headers: Webhook headers
            event_data: Event data

        Returns:
            bool: True if webhook is valid
        """
        try:
            # Basic validation - check for required headers
            required_headers = ["x-goog-channel-id", "x-goog-resource-state"]
            for header in required_headers:
                if header not in headers:
                    logger.warning("Missing required header", header=header)
                    return False

            # Validate resource state
            resource_state = headers.get("x-goog-resource-state")
            valid_states = ["exists", "not_exists", "sync"]

            if resource_state not in valid_states:
                logger.warning("Invalid resource state", resource_state=resource_state)
                return False

            logger.debug("Webhook resource state", resource_state=resource_state)

            # Verify webhook signature if secret is configured
            webhook_secret = getattr(self.settings, "google_drive_webhook_secret", None)

            if webhook_secret:
                signature = headers.get("x-goog-channel-token")
                payload = event_data.get("raw_payload", b"")

                if signature and not self._verify_webhook_signature(
                    payload, signature, webhook_secret
                ):
                    logger.warning("Invalid webhook signature")
                    return False

            logger.debug("Webhook validation passed")
            return True

        except Exception as e:
            logger.error("Webhook validation failed", error=str(e))
            return False

    def _verify_webhook_signature(
        self, payload: bytes, signature: str, secret: str
    ) -> bool:
        """
        Verify the webhook signature for security.

        Args:
            payload: Raw payload bytes
            signature: Signature from headers
            secret: Webhook secret

        Returns:
            bool: True if signature is valid
        """
        try:
            if not signature or not secret:
                return True  # Skip verification if no secret is configured

            import hmac
            import hashlib

            expected_signature = hmac.new(
                secret.encode("utf-8"), payload, hashlib.sha256
            ).hexdigest()

            return hmac.compare_digest(signature, expected_signature)

        except Exception as e:
            logger.error(f"Signature verification failed", error=str(e))
            return False

    def _determine_event_type(
        self, event: Dict[str, Any], headers: Dict[str, str]
    ) -> Optional[TriggerEventType]:
        """
        Determine the event type based on Google Drive event data and webhook headers.

        Args:
            event: Google Drive event data
            headers: Webhook headers

        Returns:
            TriggerEventType: The determined event type, or None if cannot be determined
        """
        try:
            # Get resource state from headers
            resource_state = headers.get("x-goog-resource-state", "")

            # Basic mapping based on resource state
            if resource_state == "not_exists":
                return TriggerEventType.DELETED
            elif resource_state == "sync":
                return None  # Ignore sync events
            elif resource_state == "exists":
                # For 'exists' state, we need to determine if it's created or updated
                # Check if the file was recently created (within last 5 minutes)
                if event.get("createdTime") and event.get("modifiedTime"):
                    try:
                        created_time = datetime.fromisoformat(
                            event["createdTime"].replace("Z", "+00:00")
                        )
                        modified_time = datetime.fromisoformat(
                            event["modifiedTime"].replace("Z", "+00:00")
                        )

                        # If created and modified times are very close (within 1 minute),
                        # it's likely a new file
                        time_diff = abs((modified_time - created_time).total_seconds())
                        if time_diff < 60:  # Within 1 minute
                            return TriggerEventType.CREATED
                        else:
                            return TriggerEventType.UPDATED

                    except Exception as date_error:
                        logger.warning(
                            f"Failed to parse file timestamps for type determination: {date_error}"
                        )
                        # Default to UPDATED if we can't parse timestamps
                        return TriggerEventType.UPDATED
                else:
                    # Default to UPDATED if timestamps are missing
                    return TriggerEventType.UPDATED
            else:
                logger.warning(f"Unknown resource state: {resource_state}")
                return TriggerEventType.UPDATED  # Default fallback

        except Exception as e:
            logger.error(f"Failed to determine event type: {e}")
            return TriggerEventType.UPDATED  # Safe fallback

    async def _setup_polling_trigger(
        self, trigger_config: TriggerConfiguration, service
    ) -> bool:
        """
        Set up polling-based trigger monitoring (fallback when webhooks fail).

        Args:
            trigger_config: Trigger configuration
            service: Google Drive service instance

        Returns:
            bool: True if polling setup was successful
        """
        try:
            trigger_id = trigger_config.trigger_id
            poll_interval = trigger_config.config.get("poll_interval_seconds", 300)

            # Initialize polling state
            self._polling_states[trigger_id] = {
                "last_check_time": datetime.now(timezone.utc).isoformat(),
                "user_id": trigger_config.user_id,  # Use user_id from TriggerConfiguration
                "folder_ids": trigger_config.config.get("folder_ids", []),
                "event_types": trigger_config.event_types,
                "poll_interval": poll_interval,
                "workflow_id": trigger_config.workflow_id,
                "created_at": datetime.now(),
            }

            # Start polling task
            task = asyncio.create_task(
                self._polling_loop(trigger_id, trigger_config, service)
            )
            self._polling_tasks[trigger_id] = task

            logger.info(
                "🔄 Started Google Drive user polling",
                trigger_id=trigger_id,
                user_id=trigger_config.user_id,  # Use user_id from TriggerConfiguration
                poll_interval=poll_interval,
                folder_ids=trigger_config.config.get("folder_ids"),
            )
            return True

        except Exception as e:
            logger.error(f"Failed to setup polling trigger {trigger_id}", error=str(e))
            return False

    async def _polling_loop(
        self, trigger_id: UUID, trigger_config: TriggerConfiguration, service
    ):
        """
        Main polling loop for a trigger.
        Continuously checks for new drive events.

        Args:
            trigger_id: Trigger ID
            trigger_config: Trigger configuration
            service: Google Drive service instance
        """
        try:
            poll_interval = self._polling_states[trigger_id]["poll_interval"]
            user_id = self._polling_states[trigger_id]["user_id"]
            folder_ids = self._polling_states[trigger_id]["folder_ids"]
            workflow_id = self._polling_states[trigger_id]["workflow_id"]

            logger.info("Starting polling loop for trigger", trigger_id=trigger_id)

            while trigger_id in self._polling_states:
                try:
                    # Get current state
                    state = self._polling_states[trigger_id]
                    last_check_time_str = state["last_check_time"]
                    last_check_time_dt = datetime.fromisoformat(
                        last_check_time_str.replace("Z", "+00:00")
                    )

                    logger.debug(
                        f"Checking for new events for trigger {trigger_id}",
                        last_check_time=last_check_time_str,
                    )

                    # Find newly modified files
                    newly_modified_files = await self._get_newly_modified_files(
                        service, folder_ids, last_check_time_dt
                    )

                    if newly_modified_files:
                        logger.info(
                            "📁 Found new drive events",
                            trigger_id=trigger_id,
                            user_id=user_id,
                            file_count=len(newly_modified_files),
                            folder_ids=folder_ids,
                        )

                        latest_modification_time = last_check_time_dt

                        # Process each file event and create TriggerEvents
                        for file_event in newly_modified_files:
                            try:
                                # Determine event type
                                event_type = (
                                    TriggerEventType.UPDATED
                                )  # Default for polling

                                # Check if file was recently created
                                if file_event.get("createdTime") and file_event.get(
                                    "modifiedTime"
                                ):
                                    created_time = datetime.fromisoformat(
                                        file_event["createdTime"].replace("Z", "+00:00")
                                    )
                                    modified_time = datetime.fromisoformat(
                                        file_event["modifiedTime"].replace(
                                            "Z", "+00:00"
                                        )
                                    )

                                    # If created and modified times are very close, it's likely a new file
                                    time_diff = abs(
                                        (modified_time - created_time).total_seconds()
                                    )
                                    if time_diff < 60:  # Within 1 minute
                                        event_type = TriggerEventType.CREATED

                                # Check if this event type is configured for this trigger
                                if event_type.value not in trigger_config.event_types:
                                    continue

                                # Create TriggerEvent for workflow execution
                                trigger_event = TriggerEvent(
                                    trigger_id=trigger_id,
                                    workflow_id=workflow_id,
                                    event_type=event_type,
                                    event_data=file_event,
                                    metadata={
                                        "user_id": user_id,
                                        "folder_ids": folder_ids,
                                        "timestamp": datetime.now(
                                            timezone.utc
                                        ).isoformat(),
                                        "source": "google_drive_user_polling",
                                        "detection_method": "polling",
                                    },
                                )

                                # Send to TriggerManager for workflow execution
                                from src.core.trigger_manager import TriggerManager

                                trigger_manager = TriggerManager.get_instance()
                                await trigger_manager.process_event(trigger_event)

                                # Update latest modification time
                                if file_event.get("modifiedTime"):
                                    file_modified_dt = datetime.fromisoformat(
                                        file_event["modifiedTime"].replace(
                                            "Z", "+00:00"
                                        )
                                    )
                                    if file_modified_dt > latest_modification_time:
                                        latest_modification_time = file_modified_dt

                                logger.info(
                                    "Processed drive event via polling",
                                    event_type=event_type.value,
                                    user_id=user_id,
                                    file_name=file_event.get("name", "Unknown"),
                                    file_id=file_event.get("id"),
                                    trigger_id=trigger_id,
                                    workflow_id=workflow_id,
                                )

                            except Exception as event_error:
                                logger.error(
                                    "Failed to process individual polling event",
                                    error=str(event_error),
                                    event_id=file_event.get("id", "unknown"),
                                    user_id=user_id,
                                )
                                continue

                        # Update state with latest processed time
                        new_state_time = latest_modification_time.isoformat().replace(
                            "+00:00", "Z"
                        )
                        self._polling_states[trigger_id][
                            "last_check_time"
                        ] = new_state_time

                        logger.debug(
                            f"Updated polling state for trigger {trigger_id}",
                            new_last_check_time=new_state_time,
                        )

                    # Wait for next poll
                    await asyncio.sleep(poll_interval)

                except asyncio.CancelledError:
                    logger.info(
                        "Polling loop cancelled for trigger", trigger_id=trigger_id
                    )
                    break
                except Exception as e:
                    logger.error(
                        f"Error in polling loop for trigger {trigger_id}",
                        error=str(e),
                    )
                    # Wait longer after an error
                    await asyncio.sleep(poll_interval * 2)

        except Exception as e:
            logger.error(
                f"Fatal error in polling loop for trigger {trigger_id}",
                error=str(e),
            )
        finally:
            # Clean up
            if trigger_id in self._polling_tasks:
                del self._polling_tasks[trigger_id]
            logger.info("Polling loop ended for trigger", trigger_id=trigger_id)

    async def _get_newly_modified_files(
        self, service, folder_ids: List[str], last_check_time_dt: datetime
    ) -> List[Dict[str, Any]]:
        """
        Get newly modified files since the last check time.

        Args:
            service: Google Drive service instance
            folder_ids: List of folder IDs to check
            last_check_time_dt: Last check timestamp

        Returns:
            List[Dict]: List of newly modified files
        """
        try:
            # Format time for Google Drive API
            modified_time = last_check_time_dt.isoformat().replace("+00:00", "Z")

            # Build query for recently modified files
            query_parts = [f"modifiedTime > '{modified_time}'"]

            # Add folder restrictions if specified
            if folder_ids:
                folder_queries = [
                    f"'{folder_id}' in parents" for folder_id in folder_ids
                ]
                query_parts.append(f"({' or '.join(folder_queries)})")

            query = " and ".join(query_parts)

            loop = asyncio.get_event_loop()
            files_result = await loop.run_in_executor(
                None,
                lambda: service.files()
                .list(
                    q=query,
                    pageSize=50,  # Reasonable limit
                    orderBy="modifiedTime",
                    fields="files(id,name,mimeType,parents,modifiedTime,createdTime,size,webViewLink,owners)",
                )
                .execute(),
            )

            files = files_result.get("files", [])
            return files

        except Exception as e:
            logger.error(f"Failed to get newly modified files", error=str(e))
            return []

    async def fetch_recent_files(
        self, user_id: str, max_results: int = 10, folder_ids: List[str] = None
    ) -> List[Dict[str, Any]]:
        """
        Fetch recent files from user's Google Drive.

        This is a public method that can be used to fetch real file data
        for sample/testing purposes.

        Args:
            user_id: User ID to fetch files for
            max_results: Maximum number of files to return
            folder_ids: Optional list of folder IDs to restrict search

        Returns:
            List[Dict[str, Any]]: List of drive files
        """
        try:
            # Get user credentials
            credentials = await self._get_user_credentials(user_id)
            if not credentials:
                logger.warning(f"No credentials found for user {user_id}")
                return []

            # Create Drive service
            service = await self._create_drive_service(credentials)
            if not service:
                logger.warning(f"Failed to create Drive service for user {user_id}")
                return []

            # Build query for recent files
            query_parts = []

            # Add folder restrictions if specified
            if folder_ids:
                folder_queries = [
                    f"'{folder_id}' in parents" for folder_id in folder_ids
                ]
                query_parts.append(f"({' or '.join(folder_queries)})")

            # Exclude trashed files
            query_parts.append("trashed = false")

            query = " and ".join(query_parts) if query_parts else "trashed = false"

            query_params = {
                "q": query,
                "pageSize": max_results,
                "orderBy": "modifiedTime desc",
                "fields": "files(id,name,mimeType,parents,modifiedTime,createdTime,size,webViewLink,owners)",
            }

            loop = asyncio.get_event_loop()
            files_result = await loop.run_in_executor(
                None,
                lambda: service.files().list(**query_params).execute(),
            )

            files = files_result.get("files", [])

            logger.debug(
                f"Fetched {len(files)} files for user {user_id}", folder_ids=folder_ids
            )

            return files

        except Exception as e:
            logger.warning(
                f"Failed to fetch files for user {user_id}",
                error=str(e),
                folder_ids=folder_ids,
            )
            return []

    async def _perform_health_check(self) -> bool:
        """
        Perform health check for Google Drive user adapter.

        Returns:
            bool: True if healthy, False otherwise
        """
        try:
            # Check if we can reach Google Drive API
            async with httpx.AsyncClient() as client:
                response = await client.get(
                    "https://www.googleapis.com/drive/v3/about",
                    timeout=10.0,
                )
                # We expect 401 since we're not authenticated, but service should be reachable
                return response.status_code in [200, 401, 403]

        except Exception as e:
            logger.error(f"Google Drive health check failed", error=str(e))
            return False

    def get_adapter_info(self) -> Dict[str, Any]:
        """
        Get information about the Google Drive user adapter.

        Returns:
            Dict[str, Any]: Adapter information
        """
        return {
            "trigger_type": "google_drive",
            "name": "Google Drive",
            "description": "Monitor Google Drive events for individual users using OAuth credentials and execute workflows",
            "icon_url": "https://storage.googleapis.com/ruh-dev/mcp-logos/Google_Drive.png/**********-Google_Drive.png",
            "supported_event_types": ["created", "updated", "deleted", "moved"],
            "configuration_schema": self.get_configuration_schema(),
            "sample_event_data": self.get_sample_event_data(),
            "available_fields": self.get_available_fields(),
            "setup_instructions": "Connect your Google Drive account to monitor file and folder changes. You can specify which folders to monitor, filter by file types, and choose between webhook or polling modes. Requires OAuth authentication with Google Drive access.",
        }

    def get_configuration_schema(self) -> Dict[str, Any]:
        """
        Get the JSON schema for Google Drive user adapter configuration.

        Returns:
            Dict[str, Any]: JSON schema for configuration validation
        """
        return {
            "type": "object",
            "title": "Google Drive Configuration",
            "description": "Configuration settings for Google Drive trigger. User ID is automatically provided from authentication.",
            "properties": {
                # Note: user_id is now provided via TriggerConfiguration.user_id from auth service
                "folder_ids": {
                    "type": "array",
                    "title": "Folder IDs to Monitor",
                    "description": "Optional: Specific Google Drive folder IDs to monitor. Leave empty to monitor entire drive.",
                    "items": {"type": "string", "pattern": "^[a-zA-Z0-9_-]{25,}$"},
                    "examples": [
                        [
                            "1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms",
                            "1AbcDefGhIjKlMnOpQrStUvWxYz123456789",
                        ]
                    ],
                    "default": [],
                },
                "file_types": {
                    "type": "array",
                    "title": "File Types to Monitor",
                    "description": "Optional: MIME types to monitor. Leave empty to monitor all file types.",
                    "items": {
                        "type": "string",
                        "enum": [
                            "application/pdf",
                            "application/vnd.google-apps.document",
                            "application/vnd.google-apps.spreadsheet",
                            "application/vnd.google-apps.presentation",
                            "application/vnd.google-apps.folder",
                            "image/jpeg",
                            "image/png",
                            "text/plain",
                            "application/msword",
                            "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
                        ],
                    },
                    "examples": [
                        ["application/pdf", "application/vnd.google-apps.document"]
                    ],
                    "default": [],
                },
                "use_polling": {
                    "type": "boolean",
                    "title": "Use Polling Mode",
                    "description": "Force polling mode instead of webhooks (useful for testing or when webhooks are not available)",
                    "default": False,
                },
                "poll_interval_seconds": {
                    "type": "integer",
                    "title": "Polling Interval (seconds)",
                    "description": "How often to check for changes when using polling mode",
                    "minimum": 60,
                    "maximum": 3600,
                    "default": 300,
                    "examples": [300, 600, 1800],
                },
                "webhook_ttl": {
                    "type": "integer",
                    "title": "Webhook TTL (seconds)",
                    "description": "How long webhook subscriptions should last before renewal",
                    "minimum": 3600,
                    "maximum": 2592000,
                    "default": 604800,
                    "examples": [604800, 1209600, 2592000],
                },
            },
            "required": [],  # No required fields - user_id is provided via TriggerConfiguration
            "additionalProperties": False,
        }

    def get_sample_event_data(self) -> Dict[str, Any]:
        """
        Get sample Google Drive event data.

        Returns:
            Dict[str, Any]: Sample Google Drive event structure
        """
        return {
            "kind": "drive#file",
            "id": "1mGcPHvVQ7cG8vF5xJ2kL9nM3oP4qR5sT",
            "name": "Sample Document.pdf",
            "mimeType": "application/pdf",
            "parents": ["1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms"],
            "createdTime": "2024-01-15T10:30:00.000Z",
            "modifiedTime": "2024-01-15T10:35:00.000Z",
            "size": "1048576",
            "webViewLink": "https://drive.google.com/file/d/1mGcPHvVQ7cG8vF5xJ2kL9nM3oP4qR5sT/view",
            "owners": [
                {
                    "kind": "drive#user",
                    "displayName": "John Doe",
                    "emailAddress": "<EMAIL>",
                }
            ],
        }

    def get_available_fields(self) -> List[Dict[str, str]]:
        """
        Get available fields for Google Drive events.

        Returns:
            List[Dict[str, str]]: List of available fields for mapping
        """
        return [
            {"field": "id", "description": "File ID", "type": "string"},
            {"field": "name", "description": "File name", "type": "string"},
            {"field": "mimeType", "description": "File MIME type", "type": "string"},
            {"field": "size", "description": "File size in bytes", "type": "string"},
            {
                "field": "createdTime",
                "description": "File creation time",
                "type": "string",
            },
            {
                "field": "modifiedTime",
                "description": "File modification time",
                "type": "string",
            },
            {"field": "webViewLink", "description": "File view link", "type": "string"},
            {"field": "parents", "description": "Parent folder IDs", "type": "array"},
            {"field": "owners", "description": "File owners", "type": "array"},
        ]
