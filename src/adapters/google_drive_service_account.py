"""
Google Drive Service Account adapter for handling Google Drive API operations.

This module provides a service layer for Google Drive operations including:
- Service account credential management
- Webhook subscription management
- Change processing and event mapping
- API integrations
"""

import asyncio
import json
import os
import uuid
from datetime import datetime, timezone, timedelta
from typing import Dict, List, Optional, Any, <PERSON>ple

import httpx
import requests
from google.oauth2.service_account import Credentials as ServiceAccountCredentials
from googleapiclient.discovery import build
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select

from src.database.models import GoogleDriveSubscription
from src.schemas.google_drive_subscription import (
    GoogleDriveSubscriptionCreate,
    GoogleDriveSubscriptionResponse,
)
from src.utils.config import get_settings
from src.utils.logger import get_logger

logger = get_logger(__name__)
settings = get_settings()


class GoogleDriveServiceAccountAdapter:
    """
    Google Drive Service Account adapter for managing subscriptions and processing events.
    """

    def __init__(self):
        """Initialize the Google Drive Service Account adapter."""
        self.valid_event_types = [
            "file_created",
            "file_updated",
            "file_deleted",
            "file_moved",
            "file_shared",
            "folder_created",
            "folder_updated",
            "folder_deleted",
            "all_changes",
        ]

    async def create_subscription(
        self, subscription_data: GoogleDriveSubscriptionCreate, session: AsyncSession
    ) -> GoogleDriveSubscriptionResponse:
        """
        Create a new Google Drive subscription.

        Args:
            subscription_data: Subscription creation data
            session: Database session

        Returns:
            GoogleDriveSubscriptionResponse: Created subscription details

        Raises:
            ValueError: If validation fails
            RuntimeError: If subscription creation fails
        """
        logger.info(
            "Creating Google Drive subscription",
            organization_id=subscription_data.organization_id,
            event_types=subscription_data.event_types,
        )

        # Validate credentials
        if not self._validate_credentials():
            raise RuntimeError("Service account credentials file not found or invalid")

        # Load credentials
        credentials = self._load_service_account_credentials()
        if not credentials:
            raise RuntimeError("Failed to load service account credentials")

        # Validate event types
        self._validate_event_types(subscription_data.event_types)

        # Generate unique channel ID
        channel_id = f"drive-channel-{uuid.uuid4()}"

        # Create Google Drive service
        service = build("drive", "v3", credentials=credentials)

        # Set up webhook subscription
        webhook_response = await self._setup_drive_webhook(service, channel_id)
        if not webhook_response:
            raise RuntimeError("Failed to set up Google Drive webhook subscription")

        resource_id = webhook_response.get("resourceId", "unknown")
        start_page_token = webhook_response.get("startPageToken")

        # Create subscription record
        expires_at = datetime.now(timezone.utc) + timedelta(days=7)
        subscription = GoogleDriveSubscription(
            organization_id=subscription_data.organization_id,
            event_types=subscription_data.event_types,
            channel_id=channel_id,
            resource_id=resource_id,
            start_page_token=start_page_token,
            current_page_token=start_page_token,
            expires_at=expires_at,
            is_active=True,
        )

        session.add(subscription)
        await session.commit()
        await session.refresh(subscription)

        logger.info(
            "Successfully created Google Drive subscription",
            subscription_id=subscription.id,
            organization_id=subscription_data.organization_id,
            channel_id=channel_id,
        )

        return self._subscription_to_response(subscription)

    async def list_subscriptions(
        self, session: AsyncSession
    ) -> List[GoogleDriveSubscriptionResponse]:
        """
        List all Google Drive subscriptions.

        Args:
            session: Database session

        Returns:
            List[GoogleDriveSubscriptionResponse]: List of subscriptions
        """
        result = await session.execute(select(GoogleDriveSubscription))
        subscriptions = result.scalars().all()

        return [
            self._subscription_to_response(subscription)
            for subscription in subscriptions
        ]

    async def delete_subscription(
        self, subscription_id: str, session: AsyncSession
    ) -> bool:
        """
        Delete a Google Drive subscription.

        Args:
            subscription_id: ID of subscription to delete
            session: Database session

        Returns:
            bool: True if deletion was successful

        Raises:
            ValueError: If subscription not found
        """
        result = await session.execute(
            select(GoogleDriveSubscription).where(
                GoogleDriveSubscription.id == subscription_id
            )
        )
        subscription = result.scalar_one_or_none()

        if not subscription:
            raise ValueError("Subscription not found")

        # Try to stop the webhook
        try:
            credentials = self._load_service_account_credentials()
            if credentials:
                service = build("drive", "v3", credentials=credentials)
                await self._stop_drive_webhook(
                    service, subscription.channel_id, subscription.resource_id
                )
                logger.info(
                    f"Successfully stopped webhook for subscription {subscription_id}"
                )
            else:
                logger.warning("Could not load credentials to stop webhook")
        except Exception as e:
            logger.error(
                f"Error stopping webhook for subscription {subscription_id}: {e}"
            )
            # Continue with deletion even if webhook stop fails

        await session.delete(subscription)
        await session.commit()

        logger.info(f"Deleted subscription {subscription_id}")
        return True

    async def check_expired_subscriptions(
        self, session: AsyncSession
    ) -> Dict[str, Any]:
        """
        Check and update expired subscriptions.

        Args:
            session: Database session

        Returns:
            Dict[str, Any]: Summary of expired subscriptions
        """
        result = await session.execute(
            select(GoogleDriveSubscription).where(
                GoogleDriveSubscription.is_active == True
            )
        )
        active_subscriptions = result.scalars().all()

        current_time = datetime.now(timezone.utc)
        expired_count = 0

        for subscription in active_subscriptions:
            if subscription.expires_at <= current_time:
                logger.info(f"Deactivating expired subscription {subscription.id}")
                subscription.is_active = False
                expired_count += 1

        if expired_count > 0:
            await session.commit()

        logger.info(
            f"Checked {len(active_subscriptions)} subscriptions, deactivated {expired_count} expired ones"
        )

        return {
            "success": True,
            "total_checked": len(active_subscriptions),
            "expired_deactivated": expired_count,
            "timestamp": current_time.isoformat(),
        }

    def get_health_status(self) -> Dict[str, Any]:
        """
        Get health status of Google Drive integration.

        Returns:
            Dict[str, Any]: Health status information
        """
        credentials_exist = self._validate_credentials()
        credentials_valid = False
        credentials_source = "none"

        if credentials_exist:
            try:
                credentials = self._load_service_account_credentials()
                credentials_valid = credentials is not None
                if self._has_env_credentials():
                    credentials_source = "environment_variables"
                else:
                    credentials_source = "file"
            except Exception:
                credentials_valid = False

        file_exists = os.path.exists(
            settings.google_drive_service_account_credentials_path
        )
        env_credentials_available = self._has_env_credentials()

        return {
            "healthy": credentials_exist and credentials_valid,
            "credentials_source": credentials_source,
            "credentials_valid": credentials_valid,
            "credentials_file_exists": file_exists,
            "env_credentials_available": env_credentials_available,
            "credentials_path": settings.google_drive_service_account_credentials_path,
            "timestamp": datetime.now(timezone.utc).isoformat(),
        }

    def get_supported_event_types(self) -> Dict[str, Any]:
        """
        Get list of supported event types.

        Returns:
            Dict[str, Any]: Event types information
        """
        event_types = [
            {
                "value": "file_created",
                "description": "A new file is created in the organization",
            },
            {"value": "file_updated", "description": "An existing file is modified"},
            {"value": "file_deleted", "description": "A file is deleted"},
            {
                "value": "file_moved",
                "description": "A file is moved to a different location",
            },
            {
                "value": "file_shared",
                "description": "A file's sharing permissions are changed",
            },
            {"value": "folder_created", "description": "A new folder is created"},
            {
                "value": "folder_updated",
                "description": "An existing folder is modified",
            },
            {"value": "folder_deleted", "description": "A folder is deleted"},
            {
                "value": "all_changes",
                "description": "All types of changes (files and folders)",
            },
        ]

        return {"event_types": event_types, "total_count": len(event_types)}

    async def process_webhook_event(
        self, headers: Dict[str, str], body: bytes, session: AsyncSession
    ) -> Dict[str, Any]:
        """
        Process a Google Drive webhook event.

        Args:
            headers: Request headers
            body: Request body
            session: Database session

        Returns:
            Dict[str, Any]: Processing result
        """
        logger.info("=== GOOGLE DRIVE WEBHOOK RECEIVED ===")
        logger.info(f"Headers: {json.dumps(headers, indent=2)}")

        if body:
            try:
                body_json = json.loads(body.decode())
                logger.info(f"Body: {json.dumps(body_json, indent=2)}")
            except:
                logger.info(f"Body (raw): {body.decode()}")
        else:
            logger.info("Body: (empty)")

        # Extract channel information
        channel_id = headers.get("x-goog-channel-id")
        resource_state = headers.get("x-goog-resource-state")
        message_number = headers.get("x-goog-message-number")

        logger.info(f"Channel ID: {channel_id}")
        logger.info(f"Resource State: {resource_state}")

        # Find subscription for this channel
        if channel_id:
            result = await session.execute(
                select(GoogleDriveSubscription).where(
                    GoogleDriveSubscription.channel_id == channel_id
                )
            )
            subscription = result.scalar_one_or_none()

            if subscription:
                # Check if subscription has expired
                if await self._check_and_update_expired_subscription(
                    subscription, session
                ):
                    logger.warning(
                        f"Subscription {subscription.id} has expired and was deactivated"
                    )
                    return {"success": True, "message": "Subscription expired"}

                logger.info(
                    f"Found subscription for organization: {subscription.organization_id}"
                )
                logger.info(f"Monitoring event types: {subscription.event_types}")

                # Skip sync events
                if resource_state == "sync":
                    logger.info("Sync event - skipping processing")
                    return {"success": True, "message": "Sync event processed"}

                # Check for duplicate message using message number
                if message_number and await self._is_duplicate_message(
                    subscription, message_number, session
                ):
                    logger.info(
                        f"Duplicate message detected (message_number: {message_number}) - skipping processing"
                    )
                    return {"success": True, "message": "Duplicate message skipped"}

                # Process actual events
                logger.info("=== DRIVE EVENT DETECTED ===")
                logger.info(
                    f"Processing event for organization: {subscription.organization_id}"
                )

                # Fetch and process changes
                try:
                    changes_processed = await self._fetch_and_process_changes(
                        subscription, session
                    )
                    logger.info(f"Processed {changes_processed} changes")

                    # Store message number to prevent duplicate processing
                    if message_number:
                        await self._store_processed_message(
                            subscription, message_number, session
                        )

                except Exception as e:
                    logger.error(f"Failed to fetch and process changes: {e}")

            else:
                logger.warning(f"No subscription found for channel_id: {channel_id}")

        logger.info("=== WEBHOOK PROCESSING COMPLETE ===")

        return {
            "success": True,
            "message": "Webhook processed successfully",
            "channel_id": channel_id,
            "resource_state": resource_state,
            "timestamp": datetime.now(timezone.utc).isoformat(),
        }

    # Private helper methods

    def _validate_credentials(self) -> bool:
        """Validate that credentials are available (either from env vars or file)."""
        # Check if environment variables are available
        if self._has_env_credentials():
            return True
        # Fall back to file-based credentials
        return os.path.exists(settings.google_drive_service_account_credentials_path)

    def _validate_event_types(self, event_types: List[str]) -> None:
        """
        Validate event types.

        Args:
            event_types: List of event types to validate

        Raises:
            ValueError: If invalid event types are found
        """
        invalid_events = [
            event for event in event_types if event not in self.valid_event_types
        ]
        if invalid_events:
            raise ValueError(
                f"Invalid event types: {invalid_events}. Valid types: {self.valid_event_types}"
            )

    def _load_service_account_credentials(self) -> Optional[ServiceAccountCredentials]:
        """Load service account credentials from environment variables or local file."""
        try:
            # Try to load from environment variables first
            if self._has_env_credentials():
                logger.info(
                    "Loading service account credentials from environment variables"
                )
                credentials_info = self._get_credentials_from_env()
                if credentials_info:
                    credentials = ServiceAccountCredentials.from_service_account_info(
                        credentials_info,
                        scopes=[
                            "https://www.googleapis.com/auth/drive",
                            "https://www.googleapis.com/auth/drive.file",
                            "https://www.googleapis.com/auth/drive.metadata",
                        ],
                    )
                    logger.info(
                        "Successfully loaded service account credentials from environment variables"
                    )
                    return credentials

            # Fall back to file-based credentials
            if not os.path.exists(
                settings.google_drive_service_account_credentials_path
            ):
                logger.error(
                    f"Service account credentials file not found: {settings.google_drive_service_account_credentials_path}"
                )
                return None

            logger.info("Loading service account credentials from file")
            with open(settings.google_drive_service_account_credentials_path, "r") as f:
                credentials_info = json.load(f)

            credentials = ServiceAccountCredentials.from_service_account_info(
                credentials_info,
                scopes=[
                    "https://www.googleapis.com/auth/drive",
                    "https://www.googleapis.com/auth/drive.file",
                    "https://www.googleapis.com/auth/drive.metadata",
                ],
            )

            logger.info("Successfully loaded service account credentials from file")
            return credentials

        except Exception as e:
            logger.error(f"Failed to load service account credentials: {e}")
            return None

    async def _setup_drive_webhook(
        self, service, channel_id: str
    ) -> Optional[Dict[str, Any]]:
        """Set up Google Drive webhook subscription."""
        try:
            logger.info(
                f"[WEBHOOK SETUP] Starting webhook setup with channel_id: {channel_id}"
            )

            loop = asyncio.get_event_loop()

            def setup_webhook():
                try:
                    # Get start page token
                    start_page_token_response = (
                        service.changes().getStartPageToken().execute()
                    )
                    start_page_token = start_page_token_response.get("startPageToken")
                    logger.info(
                        f"[WEBHOOK SETUP] Got start page token: {start_page_token}"
                    )

                    # Prepare webhook request
                    webhook_body = {
                        "id": channel_id,
                        "type": "web_hook",
                        "address": settings.google_drive_webhook_url,
                        "payload": True,
                        "expiration": int(
                            (datetime.now(timezone.utc) + timedelta(days=7)).timestamp()
                            * 1000
                        ),
                    }

                    logger.info(
                        f"[WEBHOOK SETUP] Webhook URL: {settings.google_drive_webhook_url}"
                    )

                    # Set up webhook
                    result = (
                        service.changes()
                        .watch(pageToken=start_page_token, body=webhook_body)
                        .execute()
                    )

                    result["startPageToken"] = start_page_token
                    return result

                except Exception as inner_e:
                    logger.error(
                        f"[WEBHOOK SETUP] Error in setup_webhook function: {inner_e}"
                    )
                    raise

            webhook_response = await loop.run_in_executor(None, setup_webhook)
            logger.info(f"Successfully set up Google Drive webhook: {webhook_response}")
            return webhook_response

        except Exception as e:
            logger.error(f"[WEBHOOK SETUP] Failed to set up Google Drive webhook: {e}")
            return None

    async def _stop_drive_webhook(
        self, service, channel_id: str, resource_id: str
    ) -> bool:
        """Stop a Google Drive webhook subscription."""
        try:
            logger.info(
                f"Stopping Google Drive webhook: channel_id={channel_id}, resource_id={resource_id}"
            )

            stop_body = {"id": channel_id, "resourceId": resource_id}
            loop = asyncio.get_event_loop()

            def stop_webhook():
                return service.channels().stop(body=stop_body).execute()

            await loop.run_in_executor(None, stop_webhook)
            logger.info(f"Successfully stopped Google Drive webhook: {channel_id}")
            return True

        except Exception as e:
            logger.error(f"Failed to stop Google Drive webhook {channel_id}: {e}")
            return False

    async def _check_and_update_expired_subscription(
        self, subscription: GoogleDriveSubscription, session: AsyncSession
    ) -> bool:
        """Check if subscription has expired and update status."""
        try:
            current_time = datetime.now(timezone.utc)

            if subscription.expires_at <= current_time and subscription.is_active:
                logger.info(f"Subscription {subscription.id} has expired, deactivating")
                subscription.is_active = False
                await session.commit()
                return True

            return False

        except Exception as e:
            logger.error(f"Error checking subscription expiration: {e}")
            return False

    async def _fetch_and_process_changes(
        self, subscription: GoogleDriveSubscription, session: AsyncSession
    ) -> int:
        """Fetch and process Google Drive changes using stored page tokens."""
        try:
            logger.info(
                f"[CHANGES] Fetching changes for subscription {subscription.id}"
            )

            credentials = self._load_service_account_credentials()
            if not credentials:
                logger.error("[CHANGES] Failed to load service account credentials")
                return 0

            service = build("drive", "v3", credentials=credentials)
            loop = asyncio.get_event_loop()

            def fetch_changes():
                page_token = subscription.current_page_token
                changes_processed = 0

                while True:
                    response = (
                        service.changes()
                        .list(pageToken=page_token, includeRemoved=True, fields="*")
                        .execute()
                    )

                    changes = response.get("changes", [])
                    next_page_token = response.get("nextPageToken")
                    new_start_page_token = response.get("newStartPageToken")

                    logger.info(f"[CHANGES] Retrieved {len(changes)} changes")

                    # Process each change
                    for change in changes:
                        try:
                            self._process_single_change_sync(change, subscription)
                            changes_processed += 1
                        except Exception as e:
                            logger.error(f"[CHANGES] Error processing change: {e}")

                    if next_page_token:
                        page_token = next_page_token
                        continue

                    if new_start_page_token:
                        return changes_processed, new_start_page_token

                    break

                return changes_processed, page_token

            changes_processed, updated_page_token = await loop.run_in_executor(
                None, fetch_changes
            )

            # Update page token
            subscription.current_page_token = updated_page_token
            await session.commit()

            logger.info(f"[CHANGES] Successfully processed {changes_processed} changes")
            return changes_processed

        except Exception as e:
            logger.error(f"[CHANGES] Failed to fetch and process changes: {e}")
            return 0

    def _process_single_change_sync(
        self, change: Dict[str, Any], subscription: GoogleDriveSubscription
    ):
        """Process a single Google Drive change synchronously."""
        try:
            change_type = change.get("type", "unknown")
            file_info = change.get("file", {})
            file_id = file_info.get("id", "unknown")
            file_name = file_info.get("name", "unknown")
            file_mime_type = file_info.get("mimeType", "unknown")

            logger.info(f"[CHANGE PROCESSING] Processing change:")
            logger.info(f"[CHANGE PROCESSING] - Type: {change_type}")
            logger.info(f"[CHANGE PROCESSING] - File: {file_name} ({file_id})")

            # Map change to event type
            event_type = self._map_change_to_event_type(change, file_info)
            logger.info(f"[CHANGE PROCESSING] - Mapped Event Type: {event_type}")

            # Check if event type is in filter
            if (
                "all_changes" not in subscription.event_types
                and event_type not in subscription.event_types
            ):
                logger.info(
                    f"[CHANGE PROCESSING] - Skipping event type {event_type} (not in filter)"
                )
                return

            logger.info(f"[CHANGE PROCESSING] ✅ PROCESSED CHANGE:")
            logger.info(
                f"[CHANGE PROCESSING] - Organization: {subscription.organization_id}"
            )
            logger.info(f"[CHANGE PROCESSING] - Event: {event_type}")
            logger.info(f"[CHANGE PROCESSING] - File: {file_name} ({file_id})")

            # Make API call
            try:
                self._make_sync_folders_api_call_sync(
                    subscription, file_info, event_type
                )
            except Exception as e:
                logger.error(
                    f"[CHANGE PROCESSING] Failed to make sync-folders API call: {e}"
                )

        except Exception as e:
            logger.error(f"[CHANGE PROCESSING] Error processing single change: {e}")

    def _map_change_to_event_type(
        self, change: Dict[str, Any], file_info: Dict[str, Any]
    ) -> str:
        """Map a Google Drive change to event type."""
        change_type = change.get("type", "unknown")
        file_mime_type = file_info.get("mimeType", "")
        trashed = file_info.get("trashed", False)

        # Handle removed/deleted files
        if change_type == "file" and change.get("removed", False):
            if "folder" in file_mime_type:
                return "folder_deleted"
            else:
                return "file_deleted"

        # Handle trashed files
        if trashed:
            if "folder" in file_mime_type:
                return "folder_deleted"
            else:
                return "file_deleted"

        # Handle file/folder changes
        if change_type == "file":
            if file_mime_type == "application/vnd.google-apps.folder":
                return "folder_updated"
            else:
                return "file_updated"

        return "file_updated"

    def _make_sync_folders_api_call_sync(
        self,
        subscription: GoogleDriveSubscription,
        file_info: Dict[str, Any],
        event_type: str,
    ) -> bool:
        """Make synchronous API call to sync-folders endpoint."""
        try:
            # Extract folder IDs
            folder_ids = []

            if file_info.get("mimeType") == "application/vnd.google-apps.folder":
                folder_ids.append(file_info.get("id", ""))
            else:
                parents = file_info.get("parents", [])
                folder_ids.extend(parents)

            if not folder_ids:
                folder_ids = ["test"]  # Default fallback

            # Prepare API call
            payload = {
                "organisation_id": subscription.organization_id,
                "folder_ids": folder_ids,
            }

            headers = {
                "accept": "application/json",
                "X-Org-Auth-Key": "hCbxAlpjFyXPd1UiJwZqTWgC20DrMb6YDxN7trz7OAB",
                "Content-Type": "application/json",
            }

            api_url = (
                "https://app-dev.rapidinnovation.dev/api/v1/google-drive/sync-folders"
            )

            logger.info(
                f"[API CALL] Making sync-folders API call for event: {event_type}"
            )

            response = requests.post(api_url, headers=headers, json=payload)

            if response.status_code == 200:
                logger.info(f"[API CALL] ✅ Sync-folders API call successful")
                return True
            else:
                logger.error(
                    f"[API CALL] ❌ Sync-folders API call failed with status {response.status_code}"
                )
                return False

        except Exception as e:
            logger.error(f"[API CALL] Error making sync-folders API call: {e}")
            return False

    async def _is_duplicate_message(
        self,
        subscription: GoogleDriveSubscription,
        message_number: str,
        session: AsyncSession,
    ) -> bool:
        """
        Check if a message has already been processed to prevent duplicate processing.

        Args:
            subscription: The subscription to check
            message_number: The Google message number from x-goog-message-number header
            session: Database session

        Returns:
            bool: True if message has already been processed
        """
        try:
            # Check if we have processed this message number recently
            # We'll use a simple approach: check if the message number is greater than
            # the last processed message number stored in the subscription
            if (
                hasattr(subscription, "last_processed_message_number")
                and subscription.last_processed_message_number
            ):
                try:
                    current_msg_num = int(message_number)
                    last_msg_num = int(subscription.last_processed_message_number)

                    # If current message number is less than or equal to last processed,
                    # it's likely a duplicate
                    if current_msg_num <= last_msg_num:
                        logger.info(
                            f"Duplicate message detected: current={current_msg_num}, last_processed={last_msg_num}"
                        )
                        return True
                except (ValueError, TypeError):
                    # If we can't parse message numbers, fall back to string comparison
                    if message_number == subscription.last_processed_message_number:
                        logger.info(
                            f"Duplicate message detected: message_number={message_number}"
                        )
                        return True

            return False

        except Exception as e:
            logger.error(f"Error checking for duplicate message: {e}")
            # If we can't determine, err on the side of processing to avoid missing events
            return False

    async def _store_processed_message(
        self,
        subscription: GoogleDriveSubscription,
        message_number: str,
        session: AsyncSession,
    ) -> None:
        """
        Store the processed message number to prevent duplicate processing.

        Args:
            subscription: The subscription to update
            message_number: The Google message number that was processed
            session: Database session
        """
        try:
            # For now, we'll add this field to the subscription model
            # In a production system, you might want a separate table for processed messages
            # with cleanup logic to prevent unbounded growth

            # Update the subscription with the last processed message number
            subscription.last_processed_message_number = message_number
            await session.commit()

            logger.debug(f"Stored processed message number: {message_number}")

        except Exception as e:
            logger.error(f"Error storing processed message number: {e}")
            # Don't raise the exception as this is not critical for the main flow

    def _has_env_credentials(self) -> bool:
        """Check if all required environment variables for service account credentials are available."""
        required_fields = [
            settings.google_drive_service_account_type,
            settings.google_drive_service_account_project_id,
            settings.google_drive_service_account_private_key_id,
            settings.google_drive_service_account_private_key,
            settings.google_drive_service_account_client_email,
            settings.google_drive_service_account_client_id,
        ]
        return all(field is not None and field.strip() for field in required_fields)

    def _get_credentials_from_env(self) -> Optional[Dict[str, str]]:
        """Get service account credentials from environment variables."""
        try:
            if not self._has_env_credentials():
                return None

            # Replace escaped newlines in private key
            private_key = settings.google_drive_service_account_private_key.replace(
                "\\n", "\n"
            )

            credentials_info = {
                "type": settings.google_drive_service_account_type,
                "project_id": settings.google_drive_service_account_project_id,
                "private_key_id": settings.google_drive_service_account_private_key_id,
                "private_key": private_key,
                "client_email": settings.google_drive_service_account_client_email,
                "client_id": settings.google_drive_service_account_client_id,
                "auth_uri": settings.google_drive_service_account_auth_uri
                or "https://accounts.google.com/o/oauth2/auth",
                "token_uri": settings.google_drive_service_account_token_uri
                or "https://oauth2.googleapis.com/token",
                "auth_provider_x509_cert_url": settings.google_drive_service_account_auth_provider_x509_cert_url
                or "https://www.googleapis.com/oauth2/v1/certs",
                "client_x509_cert_url": settings.google_drive_service_account_client_x509_cert_url,
                "universe_domain": settings.google_drive_service_account_universe_domain
                or "googleapis.com",
            }

            # Remove None values
            credentials_info = {
                k: v for k, v in credentials_info.items() if v is not None
            }

            return credentials_info

        except Exception as e:
            logger.error(f"Failed to build credentials from environment variables: {e}")
            return None

    def _subscription_to_response(
        self, subscription: GoogleDriveSubscription
    ) -> GoogleDriveSubscriptionResponse:
        """Convert subscription model to response schema."""
        return GoogleDriveSubscriptionResponse(
            id=str(subscription.id),
            organization_id=subscription.organization_id,
            event_types=subscription.event_types,
            channel_id=subscription.channel_id,
            resource_id=subscription.resource_id,
            is_active=subscription.is_active,
            start_page_token=subscription.start_page_token,
            current_page_token=subscription.current_page_token,
            expires_at=subscription.expires_at.isoformat(),
            created_at=subscription.created_at.isoformat(),
            updated_at=subscription.updated_at.isoformat(),
        )
