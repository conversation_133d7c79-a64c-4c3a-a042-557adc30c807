"""
Authentication middleware for validating bearer tokens and API keys.

This module provides middleware to validate bearer tokens and X-API-Key headers
for authentication and extract user information from the external authentication service.
"""

from typing import Optional, Dict, Any
from fastapi import HTTPException, status, Request
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from src.utils.auth_client import validate_bearer_token, AuthServiceError
from src.utils.config import get_settings
from src.utils.logger import get_logger

logger = get_logger(__name__)

# HTTP Bearer token scheme
bearer_scheme = HTTPBearer(auto_error=False)


class AuthenticationError(Exception):
    """Authentication-related errors."""

    pass


async def get_current_user(request: Request) -> Dict[str, Any]:
    """
    Extract and validate the current user from bearer token or X-API-Key header.

    This function supports two authentication methods:
    1. Bearer token authentication (validates with external auth service)
    2. X-API-Key authentication (validates against configured API key)

    Args:
        request: FastAPI request object

    Returns:
        Dict containing user information

    Raises:
        HTTPException: If authentication fails
    """
    try:
        # Check for X-API-Key header first
        api_key = request.headers.get("X-API-Key")
        if api_key:
            return await _validate_api_key(api_key)

        # Fall back to Bearer token authentication
        authorization = request.headers.get("Authorization")
        if authorization:
            return await _validate_bearer_token(authorization)

        # No authentication provided
        logger.warning("No authentication provided")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Authentication required. Use either 'Authorization: Bearer <token>' or 'X-API-Key: <key>' header",
            headers={"WWW-Authenticate": "Bearer"},
        )

    except HTTPException:
        # Re-raise HTTP exceptions as-is
        raise
    except Exception as e:
        logger.error("Unexpected error during authentication", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal authentication error",
        )


async def _validate_api_key(api_key: str) -> Dict[str, Any]:
    """
    Validate X-API-Key authentication.

    Args:
        api_key: API key from X-API-Key header

    Returns:
        Dict containing user information for API key authentication

    Raises:
        HTTPException: If API key is invalid
    """
    if not api_key:
        logger.warning("Empty API key provided")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="API key is required",
        )

    settings = get_settings()

    # Validate against configured API key
    if api_key != settings.api_key:
        logger.warning("Invalid API key provided", api_key_length=len(api_key))
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid API key",
        )

    logger.debug("Successfully authenticated with API key")

    # Return a standard user object for API key authentication
    # This allows API key users to access all endpoints that require authentication
    return {
        "id": "api-key-user",
        "email": "<EMAIL>",
        "name": "API Key User",
        "auth_method": "api_key",
    }


async def _validate_bearer_token(authorization: str) -> Dict[str, Any]:
    """
    Validate Bearer token authentication.

    Args:
        authorization: Authorization header value

    Returns:
        Dict containing user information from auth service

    Raises:
        HTTPException: If bearer token is invalid
    """
    # Check if it's a Bearer token
    if not authorization.startswith("Bearer "):
        logger.warning(
            "Invalid authorization scheme", authorization_header=authorization[:20]
        )
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid authorization scheme. Use 'Bearer <token>'",
            headers={"WWW-Authenticate": "Bearer"},
        )

    # Extract the token
    token = authorization[7:]  # Remove "Bearer " prefix

    if not token:
        logger.warning("Empty bearer token provided")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Bearer token is required",
            headers={"WWW-Authenticate": "Bearer"},
        )

    # Validate token with auth service
    try:
        user_data = await validate_bearer_token(token)

        if not user_data:
            logger.warning("Invalid bearer token")
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid or expired token",
                headers={"WWW-Authenticate": "Bearer"},
            )

        # Ensure we have required user fields
        if not user_data.get("id"):
            logger.error("User data missing required 'id' field", user_data=user_data)
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Invalid user data from authentication service",
            )

        # Add auth method to user data
        user_data["auth_method"] = "bearer_token"

        logger.debug(
            "Successfully authenticated user with bearer token",
            user_id=user_data.get("id"),
            email=user_data.get("email"),
        )

        return user_data

    except AuthServiceError as e:
        logger.error("Auth service error during token validation", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail="Authentication service temporarily unavailable",
        )


async def get_current_user_optional(request: Request) -> Optional[Dict[str, Any]]:
    """
    Extract and validate the current user from the bearer token, but don't require authentication.

    Args:
        request: FastAPI request object

    Returns:
        Dict containing user information if authenticated, None otherwise
    """
    try:
        return await get_current_user(request)
    except HTTPException:
        # Return None if authentication fails instead of raising an exception
        return None
    except Exception as e:
        logger.warning("Error during optional authentication", error=str(e))
        return None


def require_auth(request: Request) -> Dict[str, Any]:
    """
    Dependency function to require authentication for an endpoint.

    Args:
        request: FastAPI request object

    Returns:
        Dict containing user information

    Raises:
        HTTPException: If authentication fails
    """
    # This is a synchronous wrapper that will be used with Depends()
    # The actual async work is done in the endpoint
    return request


async def extract_user_id_from_request(request: Request) -> str:
    """
    Extract user ID from authenticated request.

    Args:
        request: FastAPI request object

    Returns:
        str: User ID

    Raises:
        HTTPException: If authentication fails or user ID not found
    """
    user_data = await get_current_user(request)
    user_id = user_data.get("id")

    if not user_id:
        logger.error(
            "User ID not found in authenticated user data", user_data=user_data
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="User ID not available",
        )

    return user_id


def create_auth_dependency():
    """
    Create an authentication dependency that can be used with FastAPI Depends().

    Returns:
        Callable that validates authentication and returns user data
    """

    async def auth_dependency(request: Request) -> Dict[str, Any]:
        return await get_current_user(request)

    return auth_dependency


# Create the default auth dependency
get_authenticated_user = create_auth_dependency()


async def get_current_user_api_key_only(request: Request) -> Dict[str, Any]:
    """
    Extract and validate the current user from X-API-Key header only.

    This function only supports X-API-Key authentication and will reject
    Bearer token authentication.

    Args:
        request: FastAPI request object

    Returns:
        Dict containing user information

    Raises:
        HTTPException: If authentication fails or Bearer token is provided
    """
    try:
        # Check for X-API-Key header
        api_key = request.headers.get("X-API-Key")
        if api_key:
            return await _validate_api_key(api_key)

        # Check if Bearer token was provided (not allowed for API key only endpoints)
        authorization = request.headers.get("Authorization")
        if authorization and authorization.startswith("Bearer "):
            logger.warning("Bearer token not allowed for this endpoint, use X-API-Key")
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="This endpoint requires X-API-Key authentication only. Use 'X-API-Key: <key>' header",
            )

        # No authentication provided
        logger.warning("No X-API-Key authentication provided")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="X-API-Key authentication required. Use 'X-API-Key: <key>' header",
        )

    except HTTPException:
        # Re-raise HTTP exceptions as-is
        raise
    except Exception as e:
        logger.error("Unexpected error during API key authentication", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal authentication error",
        )


def create_api_key_only_auth_dependency():
    """
    Create an API key only authentication dependency that can be used with FastAPI Depends().

    Returns:
        Callable that validates API key authentication only and returns user data
    """

    async def api_key_auth_dependency(request: Request) -> Dict[str, Any]:
        return await get_current_user_api_key_only(request)

    return api_key_auth_dependency


# Create the API key only auth dependency
get_authenticated_user_api_key_only = create_api_key_only_auth_dependency()
