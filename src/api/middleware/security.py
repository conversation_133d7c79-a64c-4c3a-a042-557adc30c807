"""
Enhanced Security Middleware for Production Deployment.

Provides comprehensive security features:
- Rate limiting with Redis backend
- Request size limits
- Security headers
- API key encryption
- Request validation
- IP whitelisting
"""

import asyncio
import hashlib
import hmac
import time
from typing import Dict, List, Optional, Set
from ipaddress import ip_address, ip_network

from fastapi import H<PERSON><PERSON><PERSON>x<PERSON>, Request, Response, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from starlette.middleware.base import BaseHTTPMiddleware
import structlog

from src.utils.config import get_settings
from src.utils.logger import get_logger

logger = get_logger(__name__)


class SecurityConfig:
    """Security configuration settings."""
    
    def __init__(self):
        self.settings = get_settings()
        
        # Rate limiting
        self.rate_limit_requests = self.settings.rate_limit_requests_per_minute
        self.rate_limit_burst = self.settings.rate_limit_burst
        self.rate_limit_window = 60  # seconds
        
        # Request limits
        self.max_request_size = 10 * 1024 * 1024  # 10MB
        self.max_json_size = 1 * 1024 * 1024  # 1MB
        
        # Security headers
        self.security_headers = {
            "X-Content-Type-Options": "nosniff",
            "X-Frame-Options": "DENY",
            "X-XSS-Protection": "1; mode=block",
            "Strict-Transport-Security": "max-age=31536000; includeSubDomains",
            "Content-Security-Policy": "default-src 'self'",
            "Referrer-Policy": "strict-origin-when-cross-origin"
        }
        
        # Allowed IPs (if configured)
        self.allowed_ips: Optional[Set[str]] = None
        if hasattr(self.settings, 'allowed_ips') and self.settings.allowed_ips:
            self.allowed_ips = set(self.settings.allowed_ips.split(','))


class RateLimiter:
    """Redis-backed rate limiter with sliding window."""
    
    def __init__(self, redis_client=None):
        self.redis_client = redis_client
        self.local_cache: Dict[str, List[float]] = {}
        self.cache_cleanup_interval = 300  # 5 minutes
        self.last_cleanup = time.time()
    
    async def is_allowed(self, key: str, limit: int, window: int) -> bool:
        """Check if request is allowed under rate limit."""
        current_time = time.time()
        
        if self.redis_client:
            return await self._redis_rate_limit(key, limit, window, current_time)
        else:
            return await self._local_rate_limit(key, limit, window, current_time)
    
    async def _redis_rate_limit(self, key: str, limit: int, window: int, current_time: float) -> bool:
        """Redis-based sliding window rate limiting."""
        try:
            pipe = self.redis_client.pipeline()
            
            # Remove old entries
            pipe.zremrangebyscore(f"rate_limit:{key}", 0, current_time - window)
            
            # Count current requests
            pipe.zcard(f"rate_limit:{key}")
            
            # Add current request
            pipe.zadd(f"rate_limit:{key}", {str(current_time): current_time})
            
            # Set expiration
            pipe.expire(f"rate_limit:{key}", window + 1)
            
            results = await pipe.execute()
            current_count = results[1]
            
            return current_count < limit
            
        except Exception as e:
            logger.error(f"Redis rate limiting error: {e}")
            # Fallback to local rate limiting
            return await self._local_rate_limit(key, limit, window, current_time)
    
    async def _local_rate_limit(self, key: str, limit: int, window: int, current_time: float) -> bool:
        """Local memory-based rate limiting (fallback)."""
        # Cleanup old entries periodically
        if current_time - self.last_cleanup > self.cache_cleanup_interval:
            await self._cleanup_local_cache(current_time, window)
            self.last_cleanup = current_time
        
        # Get or create request list for this key
        if key not in self.local_cache:
            self.local_cache[key] = []
        
        requests = self.local_cache[key]
        
        # Remove old requests
        cutoff_time = current_time - window
        self.local_cache[key] = [req_time for req_time in requests if req_time > cutoff_time]
        
        # Check if under limit
        if len(self.local_cache[key]) >= limit:
            return False
        
        # Add current request
        self.local_cache[key].append(current_time)
        return True
    
    async def _cleanup_local_cache(self, current_time: float, window: int):
        """Clean up old entries from local cache."""
        cutoff_time = current_time - window
        keys_to_remove = []
        
        for key, requests in self.local_cache.items():
            # Remove old requests
            self.local_cache[key] = [req_time for req_time in requests if req_time > cutoff_time]
            
            # Remove empty entries
            if not self.local_cache[key]:
                keys_to_remove.append(key)
        
        for key in keys_to_remove:
            del self.local_cache[key]


class SecurityMiddleware(BaseHTTPMiddleware):
    """Comprehensive security middleware."""
    
    def __init__(self, app, redis_client=None):
        super().__init__(app)
        self.config = SecurityConfig()
        self.rate_limiter = RateLimiter(redis_client)
        
        # Exempt paths from certain security checks
        self.rate_limit_exempt_paths = {"/health", "/health/live", "/health/ready"}
        self.size_limit_exempt_paths = {"/health", "/metrics"}
    
    async def dispatch(self, request: Request, call_next):
        """Process request through security checks."""
        start_time = time.time()
        
        try:
            # 1. IP Whitelisting
            if not await self._check_ip_whitelist(request):
                return self._create_error_response(
                    status.HTTP_403_FORBIDDEN,
                    "Access denied from this IP address"
                )
            
            # 2. Request size limits
            if not await self._check_request_size(request):
                return self._create_error_response(
                    status.HTTP_413_REQUEST_ENTITY_TOO_LARGE,
                    "Request too large"
                )
            
            # 3. Rate limiting
            if not await self._check_rate_limit(request):
                return self._create_error_response(
                    status.HTTP_429_TOO_MANY_REQUESTS,
                    "Rate limit exceeded"
                )
            
            # 4. Process request
            response = await call_next(request)
            
            # 5. Add security headers
            self._add_security_headers(response)
            
            # 6. Log security metrics
            duration = time.time() - start_time
            logger.debug(
                "Security check completed",
                path=request.url.path,
                method=request.method,
                duration=duration,
                status_code=response.status_code
            )
            
            return response
            
        except Exception as e:
            logger.error(f"Security middleware error: {e}", exc_info=True)
            return self._create_error_response(
                status.HTTP_500_INTERNAL_SERVER_ERROR,
                "Internal security error"
            )
    
    async def _check_ip_whitelist(self, request: Request) -> bool:
        """Check if client IP is whitelisted."""
        if not self.config.allowed_ips:
            return True  # No IP restrictions configured
        
        try:
            # Get client IP (handle proxy headers)
            client_ip = request.headers.get("X-Real-IP") or \
                       request.headers.get("X-Forwarded-For", "").split(",")[0].strip() or \
                       request.client.host
            
            # Check if IP is in allowed list
            client_addr = ip_address(client_ip)
            
            for allowed_ip in self.config.allowed_ips:
                try:
                    if "/" in allowed_ip:
                        # CIDR notation
                        if client_addr in ip_network(allowed_ip, strict=False):
                            return True
                    else:
                        # Single IP
                        if client_addr == ip_address(allowed_ip):
                            return True
                except ValueError:
                    logger.warning(f"Invalid IP configuration: {allowed_ip}")
                    continue
            
            logger.warning(f"Access denied for IP: {client_ip}")
            return False
            
        except Exception as e:
            logger.error(f"IP whitelist check error: {e}")
            return True  # Allow on error to avoid blocking legitimate traffic
    
    async def _check_request_size(self, request: Request) -> bool:
        """Check request size limits."""
        if request.url.path in self.size_limit_exempt_paths:
            return True
        
        try:
            # Check Content-Length header
            content_length = request.headers.get("content-length")
            if content_length:
                size = int(content_length)
                if size > self.config.max_request_size:
                    logger.warning(f"Request too large: {size} bytes")
                    return False
            
            # Additional check for JSON content
            content_type = request.headers.get("content-type", "")
            if "application/json" in content_type and content_length:
                size = int(content_length)
                if size > self.config.max_json_size:
                    logger.warning(f"JSON request too large: {size} bytes")
                    return False
            
            return True
            
        except Exception as e:
            logger.error(f"Request size check error: {e}")
            return True  # Allow on error
    
    async def _check_rate_limit(self, request: Request) -> bool:
        """Check rate limiting."""
        if request.url.path in self.rate_limit_exempt_paths:
            return True
        
        try:
            # Create rate limit key based on IP and endpoint
            client_ip = request.headers.get("X-Real-IP") or \
                       request.headers.get("X-Forwarded-For", "").split(",")[0].strip() or \
                       request.client.host
            
            # Different limits for different endpoint types
            if request.url.path.startswith("/webhooks/"):
                # Higher limits for webhooks
                limit = self.config.rate_limit_requests * 2
            elif request.url.path.startswith("/api/"):
                # Standard limits for API
                limit = self.config.rate_limit_requests
            else:
                # Lower limits for other endpoints
                limit = self.config.rate_limit_requests // 2
            
            rate_limit_key = f"{client_ip}:{request.url.path}"
            
            is_allowed = await self.rate_limiter.is_allowed(
                rate_limit_key,
                limit,
                self.config.rate_limit_window
            )
            
            if not is_allowed:
                logger.warning(
                    f"Rate limit exceeded for {client_ip} on {request.url.path}"
                )
            
            return is_allowed
            
        except Exception as e:
            logger.error(f"Rate limit check error: {e}")
            return True  # Allow on error
    
    def _add_security_headers(self, response: Response):
        """Add security headers to response."""
        for header, value in self.config.security_headers.items():
            response.headers[header] = value
    
    def _create_error_response(self, status_code: int, message: str) -> Response:
        """Create standardized error response."""
        return Response(
            content=f'{{"error": "{message}", "status_code": {status_code}}}',
            status_code=status_code,
            media_type="application/json"
        )


class APIKeyEncryption:
    """Utility for encrypting/decrypting API keys."""
    
    def __init__(self, secret_key: str):
        self.secret_key = secret_key.encode()
    
    def encrypt_api_key(self, api_key: str) -> str:
        """Encrypt API key for storage."""
        # Simple HMAC-based encryption (use proper encryption in production)
        return hmac.new(
            self.secret_key,
            api_key.encode(),
            hashlib.sha256
        ).hexdigest()
    
    def verify_api_key(self, api_key: str, encrypted_key: str) -> bool:
        """Verify API key against encrypted version."""
        return hmac.compare_digest(
            self.encrypt_api_key(api_key),
            encrypted_key
        )


def create_security_middleware(redis_client=None):
    """Factory function to create security middleware."""
    return lambda app: SecurityMiddleware(app, redis_client)
