"""
Webhook API routes for receiving external service events.

This module contains webhook endpoints for receiving events from external
services like Google Calendar, Slack, etc.
"""

from fastapi import (
    APIRouter,
    Request,
    HTTPException,
    status,
    BackgroundTasks,
    Depends,
    Query,
)
from typing import Dict, Any, Optional
import json
import structlog

from src.core.trigger_manager import TriggerManager
from src.api.middleware.correlation import get_correlation_id
from src.api.middleware.auth import get_authenticated_user

logger = structlog.get_logger(__name__)


router = APIRouter(prefix="/api/v1/webhooks", tags=["webhooks"])


# Dependency to get trigger manager instance
def get_trigger_manager() -> TriggerManager:
    """Get trigger manager instance."""
    if not hasattr(get_trigger_manager, "_instance"):
        from src.adapters import GoogleCalendarAdapter

        get_trigger_manager._instance = TriggerManager()

        # Register available adapters
        google_calendar_adapter = GoogleCalendarAdapter()
        get_trigger_manager._instance.register_adapter(google_calendar_adapter)

    return get_trigger_manager._instance


async def process_webhook_event(
    adapter_name: str,
    event_data: Dict[str, Any],
    trigger_manager: TriggerManager,
    correlation_id: Optional[str] = None,
) -> bool:
    """
    Process a webhook event in the background.

    Args:
        adapter_name: Name of the adapter to process the event
        event_data: Raw event data from the webhook
        trigger_manager: Trigger manager instance
        correlation_id: Correlation ID for tracking

    Returns:
        bool: True if event was processed successfully
    """
    try:
        success = await trigger_manager.process_event(adapter_name, event_data)

        if not success:
            logger.warning(
                "Webhook event processing failed",
                adapter_name=adapter_name,
                correlation_id=correlation_id,
            )

        return success

    except Exception as e:
        logger.error(
            "Webhook event processing exception",
            adapter_name=adapter_name,
            correlation_id=correlation_id,
            error=str(e),
            exc_info=True,
        )
        return False


@router.post("/google-calendar")
async def google_calendar_webhook(
    request: Request,
    background_tasks: BackgroundTasks,
    trigger_manager: TriggerManager = Depends(get_trigger_manager),
) -> Dict[str, str]:
    """
    Webhook endpoint for Google Calendar events.

    This endpoint receives webhook notifications from Google Calendar and processes them
    based on the `x-goog-resource-state` header to distinguish between verification (sync)
    and event change (notification) events.

    Args:
        request: HTTP request containing the webhook payload
        background_tasks: FastAPI background tasks for asynchronous processing
        trigger_manager: Trigger manager instance for event processing

    Returns:
        Dict[str, str]: Success response indicating the webhook was received and queued

    Raises:
        HTTPException: If an error occurs during webhook processing
    """

    try:
        correlation_id = get_correlation_id(request)

        # Get the raw body of the request
        body = await request.body()

        # Parse headers for Google Calendar webhook validation
        headers = dict(request.headers)

        # Extract the resource state from headers to determine the event type
        resource_state = headers.get("x-goog-resource-state", "")

        # Determine the event type based on the resource_state
        if resource_state == "sync":
            event_type = "verification"
        elif resource_state in ["exists", "not_exists"]:
            event_type = "notification"
        else:
            event_type = "unknown"
            logger.warning(
                "Unknown Google Calendar webhook event type",
                correlation_id=correlation_id,
                resource_state=resource_state,
            )

        # Parse the event data from the request body
        try:
            if body:
                event_data = json.loads(body.decode("utf-8"))
            else:
                event_data = {}
        except json.JSONDecodeError as e:
            logger.warning(
                "Failed to parse webhook JSON body",
                correlation_id=correlation_id,
                error=str(e),
            )
            event_data = {
                "type": "unparseable",
                "raw_body": body.decode("utf-8", errors="ignore"),
            }

        # Add the determined event type and headers to the event_data dictionary
        event_data["type"] = event_type
        event_data["webhook_headers"] = headers

        logger.debug(
            "Google Calendar webhook received",
            correlation_id=correlation_id,
            event_type=event_type,
        )

        # Queue the event for processing in the background
        background_tasks.add_task(
            process_webhook_event,
            "google_calendar",
            event_data,
            trigger_manager,
            correlation_id,
        )

        # Return a success response to acknowledge receipt of the webhook
        return {
            "status": "accepted",
            "message": "Webhook received and queued for processing",
        }

    except Exception as e:
        logger.error(
            "Google Calendar webhook processing failed",
            correlation_id=correlation_id,
            error=str(e),
            exc_info=True,
        )

        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to process webhook",
        )


@router.post("/google-drive/service-account")
async def google_drive_webhook(
    request: Request,
) -> Dict[str, Any]:
    """
    Webhook endpoint for Google Drive events.

    This endpoint receives webhook notifications from Google Drive and processes them
    using the Google Drive service layer.

    Args:
        request: HTTP request containing the webhook payload

    Returns:
        Dict[str, Any]: Success response indicating the webhook was processed

    Raises:
        HTTPException: If an error occurs during webhook processing
    """
    from src.adapters.google_drive_service_account import (
        GoogleDriveServiceAccountAdapter,
    )
    from src.database.connection import get_async_session

    try:
        # Extract headers and body
        headers = dict(request.headers)
        body = await request.body()

        # Get database session
        async for session in get_async_session():
            # Create service instance and process webhook
            drive_service = GoogleDriveServiceAccountAdapter()
            result = await drive_service.process_webhook_event(headers, body, session)

            return result

    except Exception as e:
        logger.error(
            "Google Drive webhook processing failed",
            error=str(e),
            exc_info=True,
        )

        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to process webhook",
        )


@router.post("/google-drive/user")
async def google_drive_user_webhook(
    request: Request,
) -> Dict[str, Any]:
    """
    Webhook endpoint for Google Drive user events.

    This endpoint receives webhook notifications from Google Drive for user credentials
    and processes them using the unified Google Drive adapter.

    Args:
        request: HTTP request containing the webhook payload

    Returns:
        Dict[str, Any]: Success response indicating the webhook was processed

    Raises:
        HTTPException: If an error occurs during webhook processing
    """
    from src.adapters.google_drive_unified import GoogleDriveUnifiedAdapter
    from src.utils.config import get_settings

    try:
        correlation_id = get_correlation_id(request)
        settings = get_settings()

        logger.info(
            "📨 Received Google Drive user webhook",
            correlation_id=correlation_id,
        )

        # Extract headers and body
        headers = dict(request.headers)
        body = await request.body()

        # Get Google Drive specific headers
        x_goog_channel_id = headers.get("x-goog-channel-id")
        x_goog_channel_token = headers.get("x-goog-channel-token")
        x_goog_resource_id = headers.get("x-goog-resource-id")
        x_goog_resource_uri = headers.get("x-goog-resource-uri")
        x_goog_resource_state = headers.get("x-goog-resource-state")
        x_goog_message_number = headers.get("x-goog-message-number")

        logger.info(
            "Google Drive user webhook details",
            correlation_id=correlation_id,
            channel_id=x_goog_channel_id,
            resource_state=x_goog_resource_state,
            resource_id=x_goog_resource_id,
            message_number=x_goog_message_number,
        )

        # Handle sync/verification messages
        if x_goog_resource_state == "sync":
            logger.info(
                "📋 Received sync message for Google Drive user webhook",
                correlation_id=correlation_id,
                channel_id=x_goog_channel_id,
            )
            return {"status": "ok", "message": "Sync event processed"}

        # Validate required headers
        if not x_goog_channel_id:
            logger.warning("Missing X-Goog-Channel-Id header in webhook")
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Missing X-Goog-Channel-Id header",
            )

        if not x_goog_resource_state:
            logger.warning("Missing X-Goog-Resource-State header in webhook")
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Missing X-Goog-Resource-State header",
            )

        # Verify webhook token if configured
        if (
            hasattr(settings, "google_drive_user_webhook_secret")
            and settings.google_drive_user_webhook_secret
        ):
            if x_goog_channel_token != settings.google_drive_user_webhook_secret:
                logger.warning(
                    "Invalid webhook token for Google Drive user webhook",
                    correlation_id=correlation_id,
                    channel_id=x_goog_channel_id,
                    expected_token_length=(
                        len(settings.google_drive_user_webhook_secret)
                        if settings.google_drive_user_webhook_secret
                        else 0
                    ),
                    received_token_length=(
                        len(x_goog_channel_token) if x_goog_channel_token else 0
                    ),
                )
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Invalid webhook token",
                )

        # Parse body data
        try:
            body_text = body.decode("utf-8") if body else ""
            body_data = {}
            if body_text.strip():
                try:
                    body_data = json.loads(body_text)
                except json.JSONDecodeError:
                    logger.debug("Webhook body is not valid JSON, treating as text")
                    body_data = {"raw_body": body_text}
        except Exception as e:
            logger.warning(f"Error reading webhook body: {str(e)}")
            body_data = {}

        # Collect all headers for processing
        webhook_headers = {
            "x-goog-channel-id": x_goog_channel_id,
            "x-goog-channel-token": x_goog_channel_token,
            "x-goog-resource-id": x_goog_resource_id,
            "x-goog-resource-uri": x_goog_resource_uri,
            "x-goog-resource-state": x_goog_resource_state,
            "x-goog-message-number": x_goog_message_number,
        }

        # Remove None values
        webhook_headers = {k: v for k, v in webhook_headers.items() if v is not None}

        # Create standardized event for processing
        raw_event = {
            "type": "webhook",
            "source": "google_drive_user",
            "webhook_headers": webhook_headers,
            "body": body_data,
            "timestamp": None,  # Will be set by adapter
        }

        # Get unified adapter instance and process the event
        adapter = GoogleDriveUnifiedAdapter.get_instance()

        # Process the event through the unified adapter
        trigger_event = await adapter.process_event(raw_event)

        if trigger_event:
            logger.info(
                "✅ Successfully processed Google Drive user webhook event",
                correlation_id=correlation_id,
                trigger_id=trigger_event.trigger_id,
                event_type=trigger_event.event_type,
                file_name=trigger_event.event_data.get("file_name"),
                channel_id=x_goog_channel_id,
            )
        else:
            logger.debug(
                "📝 Google Drive user webhook event processed but no trigger event generated",
                correlation_id=correlation_id,
                channel_id=x_goog_channel_id,
                resource_state=x_goog_resource_state,
            )

        # Return success response
        return {
            "status": "ok",
            "message": "Webhook processed successfully",
            "channel_id": x_goog_channel_id,
            "resource_state": x_goog_resource_state,
        }

    except HTTPException:
        # Re-raise HTTP exceptions
        raise
    except Exception as e:
        logger.error(
            "❌ Error processing Google Drive user webhook",
            correlation_id=correlation_id,
            error=str(e),
            exc_info=True,
        )

        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to process webhook",
        )
