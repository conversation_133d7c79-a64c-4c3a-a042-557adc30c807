"""
Health check API routes.

This module contains health check endpoints for monitoring service status
and dependencies.
"""

from fastapi import APIRouter, Depends, HTTPException, status, Request
from typing import Dict, Any, List
import datetime

from src.core.trigger_manager import TriggerManager
from src.database.connection import db_manager
from src.schemas.trigger import AdapterHealthResponse

from src.utils.config import get_settings


router = APIRouter(prefix="/api/v1/health", tags=["health"])


# Dependency to get trigger manager instance
def get_trigger_manager() -> TriggerManager:
    """Get trigger manager instance."""
    if not hasattr(get_trigger_manager, "_instance"):
        from src.adapters import GoogleCalendarAdapter

        get_trigger_manager._instance = TriggerManager()

        # Register available adapters
        google_calendar_adapter = GoogleCalendarAdapter()
        get_trigger_manager._instance.register_adapter(google_calendar_adapter)

    return get_trigger_manager._instance


@router.get("/")
async def basic_health_check() -> Dict[str, Any]:
    """
    Basic health check endpoint.

    This endpoint provides a simple health status without authentication
    and is suitable for load balancer health checks.

    Returns:
        Dict[str, Any]: Basic health status
    """
    return {
        "status": "healthy",
        "timestamp": datetime.datetime.now().isoformat(),
        "service": "trigger-service",
        "version": "1.0.0",
    }


@router.get(
    "/adapters",
    response_model=List[AdapterHealthResponse],
)
async def adapter_health_check(
    request: Request, trigger_manager: TriggerManager = Depends(get_trigger_manager)
) -> List[AdapterHealthResponse]:
    """
    Get health status for all registered adapters.

    Args:
        request: HTTP request for authentication
        trigger_manager: Trigger manager instance

    Returns:
        List[AdapterHealthResponse]: List of adapter health statuses

    Raises:
        HTTPException: If authentication fails or health check fails
    """

    try:
        adapter_health = await trigger_manager.health_check()

        health_responses = []
        for adapter_name, health_status in adapter_health.items():
            # Get adapter statistics
            adapter = trigger_manager.get_adapter(adapter_name)
            active_triggers = adapter.get_trigger_count() if adapter else 0

            health_response = AdapterHealthResponse(
                adapter_name=adapter_name,
                is_healthy=health_status.is_healthy,
                last_check=health_status.last_check,
                error_message=health_status.error_message,
                active_triggers=active_triggers,
                external_service_status=health_status.external_service_status,
            )
            health_responses.append(health_response)

        return health_responses

    except Exception as e:

        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve adapter health status",
        )
