# """
# Google Calendar adapter management API routes.

# This module provides endpoints for managing Google Calendar webhook channels,
# monitoring status, and performing maintenance operations.
# """

# from typing import Dict, Any
# from fastapi import APIRouter, Depends, HTTPException, Request
# from fastapi.responses import JSONResponse

# from src.schemas.trigger import (
#     WebhookStatusResponse,
#     WebhookCleanupResponse,
#     WebhookStopRequest,
#     WebhookStopResponse,
# )
# from src.adapters.google_calendar import GoogleCalendarAdapter
# from src.api.middleware.auth import get_authenticated_user

# router = APIRouter(prefix="/api/v1/adapters/google_calendar", tags=["Google Calendar"])


# def get_google_calendar_adapter() -> GoogleCalendarAdapter:
#     """Get Google Calendar adapter instance."""
#     # In a real application, this would be injected via dependency injection
#     if not hasattr(get_google_calendar_adapter, "_instance"):
#         get_google_calendar_adapter._instance = GoogleCalendarAdapter()
#     return get_google_calendar_adapter._instance


# @router.get(
#     "/webhook_status",
#     response_model=WebhookStatusResponse,
#     summary="Get Google Calendar webhook channel status",
#     description="""
#     **Get comprehensive status of all active Google Calendar webhook channels.**

#     This endpoint provides detailed information about webhook channels including expiration times,
#     user associations, and channel health for monitoring and management purposes.

#     **Information Provided:**
#     - Total number of active webhook channels
#     - Number of expired channels requiring cleanup
#     - Per-channel details including expiration times
#     - User associations and calendar mappings
#     - Channel health and connectivity status

#     **Use Cases:**
#     - Monitor webhook channel health
#     - Identify expired channels for cleanup
#     - Debug webhook delivery issues
#     - Audit user webhook subscriptions
#     - Capacity planning for webhook limits

#     **Authentication:** Requires valid API key or Bearer token
#     """,
#     responses={
#         200: {
#             "description": "Webhook status retrieved successfully",
#             "content": {
#                 "application/json": {
#                     "example": {
#                         "total_channels": 15,
#                         "active_channels": 12,
#                         "expired_channels": 3,
#                         "channels": [
#                             {
#                                 "channel_id": "webhook-channel-123",
#                                 "resource_id": "google-resource-456",
#                                 "user_id": "user-789",
#                                 "calendar_id": "primary",
#                                 "expiration": "2023-12-14T14:45:30.000Z",
#                                 "is_expired": False,
#                                 "event_types": ["created", "updated", "deleted"],
#                                 "created_at": "2023-12-07T14:45:30.000Z",
#                             }
#                         ],
#                         "summary": {
#                             "healthy_channels": 12,
#                             "expiring_soon": 2,
#                             "needs_renewal": 3,
#                         },
#                     }
#                 }
#             },
#         },
#         401: {
#             "description": "Authentication required",
#             "content": {
#                 "application/json": {
#                     "example": {"detail": "Authentication credentials required"}
#                 }
#             },
#         },
#         500: {
#             "description": "Internal server error",
#             "content": {
#                 "application/json": {
#                     "example": {"detail": "Failed to retrieve webhook status"}
#                 }
#             },
#         },
#     },
#     tags=["Google Calendar"],
# )
# async def get_webhook_status(
#     current_user: Dict[str, Any] = Depends(get_authenticated_user),
#     adapter: GoogleCalendarAdapter = Depends(get_google_calendar_adapter),
# ) -> WebhookStatusResponse:
#     """
#     Get the status of all active Google Calendar webhook channels.

#     Returns information about active channels, expiration times, and user associations.
#     """
#     try:

#         # Get webhook status from adapter
#         status_data = await adapter.get_webhook_status()

#         if "error" in status_data:
#             raise HTTPException(status_code=500, detail=status_data["error"])

#         return WebhookStatusResponse(**status_data)

#     except HTTPException:
#         raise
#     except Exception as e:

#         raise HTTPException(status_code=500, detail="Failed to get webhook status")


# @router.post(
#     "/cleanup_channels",
#     response_model=WebhookCleanupResponse,
#     summary="Clean up expired Google Calendar webhook channels",
#     description="""
#     **Remove expired Google Calendar webhook channels and clean up associated resources.**

#     This endpoint performs comprehensive cleanup of expired webhook subscriptions, freeing up
#     resources and ensuring optimal performance of the Google Calendar integration.

#     **Cleanup Operations:**
#     - Identify and remove expired webhook channels
#     - Clean up associated database records
#     - Free up Google API webhook quotas
#     - Remove cached user credentials for expired channels
#     - Update internal channel tracking

#     **Safety Features:**
#     - Only removes genuinely expired channels
#     - Preserves active channels and their data
#     - Provides detailed cleanup report
#     - Logs all cleanup operations for audit

#     **Use Cases:**
#     - Regular maintenance operations
#     - Troubleshooting webhook issues
#     - Freeing up webhook quota limits
#     - Cleaning up after user deactivations

#     **Recommended Frequency:** Run daily or weekly depending on user activity
#     """,
#     responses={
#         200: {
#             "description": "Cleanup completed successfully",
#             "content": {
#                 "application/json": {
#                     "examples": {
#                         "successful_cleanup": {
#                             "summary": "Successful cleanup operation",
#                             "value": {
#                                 "cleaned_channels": 5,
#                                 "remaining_channels": 10,
#                                 "errors": [],
#                                 "details": {
#                                     "expired_channels_removed": 5,
#                                     "database_records_cleaned": 15,
#                                     "cached_credentials_cleared": 3,
#                                     "quota_freed": 5,
#                                 },
#                                 "cleanup_timestamp": "2023-12-07T14:45:30.123Z",
#                             },
#                         },
#                         "partial_cleanup": {
#                             "summary": "Cleanup with some errors",
#                             "value": {
#                                 "cleaned_channels": 3,
#                                 "remaining_channels": 12,
#                                 "errors": [
#                                     "Failed to remove channel webhook-123: Google API error",
#                                     "Database cleanup failed for channel webhook-456",
#                                 ],
#                                 "cleanup_timestamp": "2023-12-07T14:45:30.123Z",
#                             },
#                         },
#                     }
#                 }
#             },
#         },
#         401: {
#             "description": "Authentication required",
#             "content": {
#                 "application/json": {
#                     "example": {"detail": "Authentication credentials required"}
#                 }
#             },
#         },
#         500: {
#             "description": "Internal server error during cleanup",
#             "content": {
#                 "application/json": {
#                     "example": {
#                         "detail": "Cleanup operation failed due to system error"
#                     }
#                 }
#             },
#         },
#     },
#     tags=["Google Calendar"],
# )
# async def cleanup_expired_channels(
#     current_user: Dict[str, Any] = Depends(get_authenticated_user),
#     adapter: GoogleCalendarAdapter = Depends(get_google_calendar_adapter),
# ) -> WebhookCleanupResponse:
#     """
#     Clean up expired Google Calendar webhook channels.

#     This endpoint removes expired webhook subscriptions and cleans up associated resources.
#     """
#     try:

#         # Get initial status
#         initial_status = await adapter.get_webhook_status()
#         initial_count = initial_status.get("total_channels", 0)

#         # Perform cleanup
#         cleaned_count = await adapter.cleanup_expired_channels()

#         # Get final status
#         final_status = await adapter.get_webhook_status()
#         remaining_count = final_status.get("total_channels", 0)

#         return WebhookCleanupResponse(
#             cleaned_channels=cleaned_count,
#             remaining_channels=remaining_count,
#             errors=[],
#         )

#     except Exception as e:

#         return WebhookCleanupResponse(
#             cleaned_channels=0,
#             remaining_channels=0,
#             errors=[str(e)],
#         )


# @router.post(
#     "/stop_channel",
#     response_model=WebhookStopResponse,
#     summary="Stop a specific Google Calendar webhook channel",
#     description="""
#     **Stop and remove a specific Google Calendar webhook channel.**

#     This endpoint allows you to manually stop a specific webhook channel, removing it from
#     active monitoring and freeing up associated resources.

#     **Operations Performed:**
#     - Stop the webhook channel with Google Calendar API
#     - Remove channel from internal tracking
#     - Clean up associated database records
#     - Clear cached user credentials if no other channels exist
#     - Update webhook status tracking

#     **Use Cases:**
#     - Manual channel management
#     - Troubleshooting specific webhook issues
#     - User-requested webhook removal
#     - Emergency channel shutdown
#     - Testing webhook lifecycle

#     **Request Body:**
#     - `channel_id` (string, required): The unique identifier of the webhook channel to stop
#       - Format: Channel ID as returned by webhook status endpoint
#       - Example: "webhook-channel-123"

#     **Safety Notes:**
#     - Only stops the specified channel
#     - Does not affect other user channels
#     - Operation is irreversible
#     - Channel will need to be recreated if monitoring is needed again
#     """,
#     responses={
#         200: {
#             "description": "Channel stopped successfully",
#             "content": {
#                 "application/json": {
#                     "examples": {
#                         "successful_stop": {
#                             "summary": "Channel stopped successfully",
#                             "value": {
#                                 "success": True,
#                                 "message": "Channel webhook-channel-123 stopped successfully",
#                                 "channel_id": "webhook-channel-123",
#                                 "stopped_at": "2023-12-07T14:45:30.123Z",
#                                 "details": {
#                                     "google_api_notified": True,
#                                     "database_cleaned": True,
#                                     "cache_cleared": True,
#                                 },
#                             },
#                         },
#                         "channel_not_found": {
#                             "summary": "Channel not found or already stopped",
#                             "value": {
#                                 "success": False,
#                                 "message": "Channel webhook-channel-456 not found or already stopped",
#                                 "channel_id": "webhook-channel-456",
#                             },
#                         },
#                     }
#                 }
#             },
#         },
#         400: {
#             "description": "Invalid request parameters",
#             "content": {
#                 "application/json": {
#                     "example": {
#                         "detail": "Invalid channel_id format or missing required fields"
#                     }
#                 }
#             },
#         },
#         401: {
#             "description": "Authentication required",
#             "content": {
#                 "application/json": {
#                     "example": {"detail": "Authentication credentials required"}
#                 }
#             },
#         },
#         404: {
#             "description": "Channel not found",
#             "content": {
#                 "application/json": {
#                     "example": {"detail": "Webhook channel not found in system"}
#                 }
#             },
#         },
#         500: {
#             "description": "Internal server error during channel stop",
#             "content": {
#                 "application/json": {
#                     "example": {
#                         "detail": "Failed to stop channel due to Google API error"
#                     }
#                 }
#             },
#         },
#     },
#     tags=["Google Calendar"],
# )
# async def stop_webhook_channel(
#     request_data: WebhookStopRequest,
#     current_user: Dict[str, Any] = Depends(get_authenticated_user),
#     adapter: GoogleCalendarAdapter = Depends(get_google_calendar_adapter),
# ) -> WebhookStopResponse:
#     """
#     Stop a specific Google Calendar webhook channel.

#     This endpoint stops a webhook channel and removes it from active monitoring.
#     """
#     try:

#         channel_id = request_data.channel_id

#         # Stop the channel
#         success = await adapter.stop_webhook_channel(channel_id)

#         if success:
#             return WebhookStopResponse(
#                 success=True,
#                 message=f"Channel {channel_id} stopped successfully",
#                 channel_id=channel_id,
#             )
#         else:
#             return WebhookStopResponse(
#                 success=False,
#                 message=f"Channel {channel_id} not found or already stopped",
#                 channel_id=channel_id,
#             )

#     except Exception as e:

#         return WebhookStopResponse(
#             success=False,
#             message=f"Error stopping channel: {str(e)}",
#             channel_id=request_data.channel_id,
#         )


# @router.get(
#     "/health",
#     summary="Google Calendar adapter health check",
#     description="""
#     **Comprehensive health check for Google Calendar adapter and integration.**

#     This endpoint provides detailed health status and metrics for the Google Calendar adapter,
#     including webhook channel status, user credential health, and API connectivity.

#     **Health Metrics Provided:**
#     - Overall adapter health status
#     - Active webhook channel count and status
#     - Expired channel count requiring cleanup
#     - Polling task status and count
#     - Cached user credential count
#     - Google Calendar API connectivity
#     - Database connectivity status

#     **Use Cases:**
#     - System monitoring and alerting
#     - Troubleshooting calendar integration issues
#     - Performance monitoring and optimization
#     - Capacity planning for webhook limits
#     - Debugging user authentication issues

#     **Monitoring Integration:** Designed for integration with monitoring systems
#     """,
#     responses={
#         200: {
#             "description": "Health status retrieved successfully",
#             "content": {
#                 "application/json": {
#                     "examples": {
#                         "healthy_adapter": {
#                             "summary": "Healthy adapter with active channels",
#                             "value": {
#                                 "adapter_name": "google_calendar",
#                                 "is_healthy": True,
#                                 "webhook_channels": {
#                                     "total": 15,
#                                     "expired": 2,
#                                     "active": 13,
#                                 },
#                                 "polling_tasks": 5,
#                                 "cached_credentials": 8,
#                                 "api_connectivity": {
#                                     "google_calendar_api": "healthy",
#                                     "last_successful_call": "2023-12-07T14:45:30.123Z",
#                                 },
#                                 "performance_metrics": {
#                                     "avg_webhook_response_time": "150ms",
#                                     "successful_webhook_deliveries": 98.5,
#                                     "failed_api_calls_last_hour": 0,
#                                 },
#                                 "timestamp": "2023-12-07T14:45:30.123Z",
#                             },
#                         },
#                         "unhealthy_adapter": {
#                             "summary": "Adapter with issues detected",
#                             "value": {
#                                 "adapter_name": "google_calendar",
#                                 "is_healthy": False,
#                                 "error": "Multiple expired channels detected, API quota exceeded",
#                                 "webhook_channels": {
#                                     "total": 20,
#                                     "expired": 15,
#                                     "active": 5,
#                                 },
#                                 "polling_tasks": 0,
#                                 "cached_credentials": 3,
#                                 "issues": [
#                                     "High number of expired webhook channels",
#                                     "Google API quota exceeded",
#                                     "No active polling tasks",
#                                 ],
#                                 "timestamp": "2023-12-07T14:45:30.123Z",
#                             },
#                         },
#                     }
#                 }
#             },
#         },
#         401: {
#             "description": "Authentication required",
#             "content": {
#                 "application/json": {
#                     "example": {"detail": "Authentication credentials required"}
#                 }
#             },
#         },
#         500: {
#             "description": "Internal server error during health check",
#             "content": {
#                 "application/json": {
#                     "example": {"detail": "Health check system failure"}
#                 }
#             },
#         },
#     },
#     tags=["Google Calendar"],
# )
# async def get_adapter_health(
#     current_user: Dict[str, Any] = Depends(get_authenticated_user),
#     adapter: GoogleCalendarAdapter = Depends(get_google_calendar_adapter),
# ) -> Dict[str, Any]:
#     """
#     Get health status and metrics for the Google Calendar adapter.

#     Returns information about adapter health, active triggers, and webhook status.
#     """
#     try:
#         # Get webhook status
#         webhook_status = await adapter.get_webhook_status()

#         # Get basic health info
#         health_info = {
#             "adapter_name": "google_calendar",
#             "is_healthy": True,
#             "webhook_channels": {
#                 "total": webhook_status.get("total_channels", 0),
#                 "expired": webhook_status.get("expired_channels", 0),
#                 "active": webhook_status.get("total_channels", 0)
#                 - webhook_status.get("expired_channels", 0),
#             },
#             "polling_tasks": len(getattr(adapter, "_polling_tasks", {})),
#             "cached_credentials": len(getattr(adapter, "_user_credentials", {})),
#         }

#         return health_info

#     except Exception as e:

#         return {
#             "adapter_name": "google_calendar",
#             "is_healthy": False,
#             "error": str(e),
#         }
