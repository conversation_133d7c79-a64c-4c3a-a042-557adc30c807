"""
Trigger management API endpoints.

This module provides REST API endpoints for managing triggers including
CRUD operations and trigger lifecycle management.
"""

from fastapi import APIRouter, HTTPException, Depends, Query, Request, Response, status
from typing import List, Optional, Dict, Any
from uuid import UUID

from src.schemas.trigger import (
    TriggerCreate,
    TriggerUpdate,
    TriggerResponse,
    TriggerListResponse,
    TriggerToggleRequest,
    TriggerFilterRequest,
    TriggerStatsResponse,
    TriggerExecutionResponse,
    TriggerTypesResponse,
    TriggerTypeInfo,
)
from src.core.trigger_manager import TriggerManager
from src.api.middleware.auth import get_authenticated_user
from src.utils.logger import get_logger

logger = get_logger(__name__)

router = APIRouter(prefix="/api/v1/triggers", tags=["Triggers"])


# Dependency to get trigger manager instance
def get_trigger_manager() -> TriggerManager:
    """Get trigger manager instance."""
    # Use the singleton instance from TriggerManager
    return TriggerManager.get_instance()


async def _fetch_real_event_data(
    adapter, trigger_type: str, user_id: str
) -> Optional[Dict[str, Any]]:
    """
    Fetch real event data from the user's connected service.

    Args:
        adapter: The adapter instance for the trigger type
        trigger_type: Type of trigger (e.g., 'google_calendar')
        user_id: ID of the authenticated user

    Returns:
        Optional[Dict[str, Any]]: Real event data if available, None otherwise
    """
    try:
        if trigger_type == "google_calendar":
            return await _fetch_google_calendar_event(adapter, user_id)
        elif trigger_type in ["google_drive_user", "google_drive"]:
            return await _fetch_google_drive_event(adapter, user_id)
        else:
            logger.debug(
                f"Real event fetching not implemented for trigger type: {trigger_type}"
            )
            return None
    except Exception as e:
        logger.warning(
            f"Failed to fetch real event data for {trigger_type}", error=str(e)
        )
        return None


async def _fetch_google_calendar_event(
    adapter, user_id: str
) -> Optional[Dict[str, Any]]:
    """Fetch a real Google Calendar event for the user."""
    try:
        # Try to fetch recent events from the user's primary calendar
        events = await adapter.fetch_recent_events(
            user_id=user_id, calendar_id="primary", max_results=1
        )

        if events and len(events) > 0:
            # Return the first event
            return events[0]

        logger.debug(f"No recent events found for user {user_id}")
        return None

    except Exception as e:
        logger.warning(
            f"Failed to fetch Google Calendar events for user {user_id}", error=str(e)
        )
        return None


async def _fetch_google_drive_event(adapter, user_id: str) -> Optional[Dict[str, Any]]:
    """Fetch a real Google Drive file for the user."""
    try:
        # Try to fetch recent files from the user's Drive
        files = await adapter.fetch_recent_files(user_id=user_id, max_results=1)

        if files and len(files) > 0:
            # Return the first file
            return files[0]

        logger.debug(f"No recent files found for user {user_id}")
        return None

    except Exception as e:
        logger.warning(
            f"Failed to fetch Google Drive files for user {user_id}", error=str(e)
        )
        return None


@router.get("/types", response_model=TriggerTypesResponse)
async def get_trigger_types(
    response: Response,
    trigger_manager: TriggerManager = Depends(get_trigger_manager),
) -> TriggerTypesResponse:
    """
    Get comprehensive information about all available trigger types.

    This endpoint is public and does not require authentication.
    It returns detailed information about all supported trigger types including
    their configuration schemas, sample event data, available fields, and setup instructions.

    Returns:
        TriggerTypesResponse: Comprehensive information about all available trigger types

    Raises:
        HTTPException: If there's an error retrieving trigger types
    """
    try:
        # Get all registered adapter names from the trigger manager
        adapter_names = trigger_manager.list_adapters()

        trigger_types = []
        for adapter_name in adapter_names:
            # Get the adapter instance
            adapter = trigger_manager.get_adapter(adapter_name)
            if not adapter:
                continue

            # Get adapter information
            adapter_info = adapter.get_adapter_info()

            # Create comprehensive trigger type info
            trigger_type = TriggerTypeInfo(
                trigger_type=adapter_info.get("trigger_type", adapter_name),
                name=adapter_info.get("name", adapter_name.replace("_", " ").title()),
                description=adapter_info.get(
                    "description", f"{adapter_name} trigger adapter"
                ),
                icon_url=adapter_info.get("icon_url", ""),
                supported_event_types=adapter_info.get("supported_event_types", []),
                configuration_schema=adapter_info.get("configuration_schema", {}),
                sample_event_data=adapter_info.get("sample_event_data", {}),
                available_fields=adapter_info.get("available_fields", []),
                setup_instructions=adapter_info.get("setup_instructions", ""),
            )
            trigger_types.append(trigger_type)

        # Add caching headers for better performance (trigger types don't change often)
        response.headers["Cache-Control"] = "public, max-age=300"  # 5 minutes
        response.headers["ETag"] = (
            f'"{len(trigger_types)}-{hash(str(sorted([t.trigger_type for t in trigger_types])))}"'
        )

        return TriggerTypesResponse(
            trigger_types=trigger_types, total_count=len(trigger_types)
        )

    except Exception as e:
        logger.error("Error retrieving trigger types", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error while retrieving trigger types",
        )


@router.get("/types/{trigger_type}/sample-event")
async def get_trigger_sample_event(
    trigger_type: str,
    trigger_manager: TriggerManager = Depends(get_trigger_manager),
    current_user: Optional[Dict[str, Any]] = Depends(get_authenticated_user),
) -> Dict[str, Any]:
    """
    Get sample event data for a specific trigger type.

    This endpoint returns raw event data that can be used for understanding
    the structure and fields available for template processing.

    **Authentication**: Optional - If provided, attempts to fetch real event data from the user's account.
    If not provided or if fetching real data fails, returns static sample data.

    **Example Usage:**
    ```bash
    # Get static sample data (no auth)
    curl -X GET "http://localhost:8000/api/v1/triggers/types/google_calendar/sample-event"

    # Get real event data (with auth)
    curl -X GET "http://localhost:8000/api/v1/triggers/types/google_calendar/sample-event" \
         -H "Authorization: Bearer <your-token>"
    ```

    Args:
        trigger_type: The trigger type to get sample data for (e.g., 'google_calendar')
        trigger_manager: Trigger manager instance
        current_user: Optional authenticated user information

    Returns:
        Dict[str, Any]: Raw event data (either static sample or real user data)

    Raises:
        HTTPException: If trigger type not found or error retrieving sample data
    """
    try:
        # Get the adapter for the specified trigger type
        adapter = trigger_manager.get_adapter(trigger_type)
        if not adapter:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Trigger type '{trigger_type}' not found",
            )

        # Get adapter information
        adapter_info = adapter.get_adapter_info()

        # Try to fetch real event data if user is authenticated
        sample_event_data = adapter_info.get("sample_event_data", {})

        if current_user:
            try:
                user_id = current_user.get("id")
                if user_id:
                    real_event_data = await _fetch_real_event_data(
                        adapter, trigger_type, user_id
                    )
                    if real_event_data:
                        sample_event_data = real_event_data
                        logger.info(
                            f"Fetched real event data for user {user_id} and trigger type {trigger_type}"
                        )
            except Exception as e:
                logger.warning(
                    f"Failed to fetch real event data for trigger type {trigger_type}",
                    error=str(e),
                )
                # Fall back to static sample data

        return sample_event_data

    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            f"Failed to get sample event data for trigger type {trigger_type}",
            error=str(e),
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve sample event data",
        )


@router.post(
    "/",
    response_model=TriggerResponse,
    status_code=status.HTTP_201_CREATED,
    summary="Create a new trigger",
    description="""
    Create a new trigger for workflow automation.

    **Authentication Required**: This endpoint requires authentication using either:
    - Bearer token in Authorization header: `Authorization: Bearer <your-api-key>`
    - API key in X-API-Key header: `X-API-Key: <your-api-key>`

    **Example Request**:
    ```bash
    curl -X POST "http://localhost:8000/api/v1/triggers/" \\
         -H "Authorization: Bearer abc" \\
         -H "Content-Type: application/json" \\
         -d '{
           "user_id": "user123",
           "workflow_id": "workflow456",
           "trigger_type": "google_calendar",
           "trigger_name": "My Calendar Trigger",
           "trigger_config": {"calendar_id": "primary"},
           "event_types": ["created", "updated"]
         }'
    ```
    """,
    responses={
        201: {"description": "Trigger created successfully"},
        400: {"description": "Invalid request data"},
        401: {"description": "Authentication required"},
        500: {"description": "Internal server error"},
    },
)
async def create_trigger(
    trigger_data: TriggerCreate,
    current_user: Dict[str, Any] = Depends(get_authenticated_user),
    trigger_manager: TriggerManager = Depends(get_trigger_manager),
) -> TriggerResponse:
    """
    Create a new trigger.

    Args:
        trigger_data: Trigger creation data
        current_user: Authenticated user information from bearer token
        trigger_manager: Trigger manager instance

    Returns:
        TriggerResponse: Created trigger details

    Raises:
        HTTPException: If trigger creation fails
    """

    # Extract user ID from authenticated user
    user_id = current_user.get("id")
    if not user_id:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="User ID not available from authentication",
        )

    try:
        # Merge input_values into trigger_config if provided
        final_trigger_config = trigger_data.trigger_config.copy()
        if trigger_data.input_values:
            final_trigger_config["input_values"] = trigger_data.input_values

        # Create internal trigger data object
        class InternalTriggerCreate:
            def __init__(
                self,
                user_id: str,
                trigger_data: TriggerCreate,
                final_config: Dict[str, Any],
            ):
                self.user_id = user_id
                self.workflow_id = trigger_data.workflow_id
                self.trigger_type = trigger_data.trigger_type
                self.trigger_name = trigger_data.trigger_name
                self.trigger_config = final_config
                self.event_types = trigger_data.event_types

        internal_trigger_data = InternalTriggerCreate(
            user_id, trigger_data, final_trigger_config
        )

        # Use the modern create_trigger method
        async with trigger_manager.db_manager.get_async_session(
            auto_commit=False
        ) as session:
            created_trigger = await trigger_manager.create_trigger(
                internal_trigger_data, session
            )

            if not created_trigger:
                # Check if it's a constraint violation
                existing_triggers = await trigger_manager.get_triggers_for_user(user_id)
                name_conflict = any(
                    t.trigger_name == trigger_data.trigger_name
                    and t.workflow_id == trigger_data.workflow_id
                    for t in existing_triggers
                )

                if name_conflict:
                    raise HTTPException(
                        status_code=status.HTTP_409_CONFLICT,
                        detail=f"A trigger with the name '{trigger_data.trigger_name}' already exists for this workflow. "
                        "Trigger names must be unique within each workflow.",
                    )

                # Generic failure message for other cases
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Failed to create trigger",
                )

            await session.commit()
            trigger_id = created_trigger.id

        # Get the created trigger details
        triggers = await trigger_manager.get_triggers_for_user(user_id)
        created_trigger = next((t for t in triggers if t.id == trigger_id), None)

        if not created_trigger:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Trigger created but could not retrieve details",
            )

        return TriggerResponse.from_orm(created_trigger)

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error creating trigger: {e}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error while creating trigger",
        )


@router.get(
    "/",
    response_model=TriggerListResponse,
    summary="List triggers",
    description="""
    List triggers with optional filtering and pagination.

    **Authentication Required**: This endpoint requires authentication using either:
    - Bearer token in Authorization header: `Authorization: Bearer <your-api-key>`
    - API key in X-API-Key header: `X-API-Key: <your-api-key>`

    **Filtering**: If no filter parameters are provided, returns all triggers with pagination.
    - `user_id`: Filter by specific user (optional)
    - `workflow_id`: Filter by specific workflow (optional)
    - `trigger_type`: Filter by trigger type (optional)
    - `is_active`: Filter by active status (optional)

    **Example Requests**:
    ```bash
    # Get all triggers (paginated)
    curl -X GET "http://localhost:8000/api/v1/triggers/" \\
         -H "Authorization: Bearer abc"

    # Get triggers for specific user
    curl -X GET "http://localhost:8000/api/v1/triggers/?user_id=user123" \\
         -H "Authorization: Bearer abc"

    # Get triggers with pagination
    curl -X GET "http://localhost:8000/api/v1/triggers/?page=2&page_size=10" \\
         -H "Authorization: Bearer abc"
    ```
    """,
    responses={
        200: {"description": "List of triggers retrieved successfully"},
        401: {"description": "Authentication required"},
        500: {"description": "Internal server error"},
    },
)
async def list_triggers(
    current_user: Dict[str, Any] = Depends(get_authenticated_user),
    user_id: Optional[str] = Query(None, description="Filter by user ID (admin only)"),
    workflow_id: Optional[str] = Query(None, description="Filter by workflow ID"),
    trigger_type: Optional[str] = Query(None, description="Filter by trigger type"),
    is_active: Optional[bool] = Query(None, description="Filter by active status"),
    page: int = Query(1, ge=1, description="Page number"),
    page_size: int = Query(20, ge=1, le=100, description="Items per page"),
    trigger_manager: TriggerManager = Depends(get_trigger_manager),
) -> TriggerListResponse:
    """
    List triggers with optional filtering and pagination.

    Args:
        current_user: Authenticated user information from bearer token
        user_id: Optional user ID filter (admin only, defaults to current user)
        workflow_id: Optional workflow ID filter
        trigger_type: Optional trigger type filter
        is_active: Optional active status filter
        page: Page number for pagination
        page_size: Number of items per page
        trigger_manager: Trigger manager instance

    Returns:
        TriggerListResponse: Paginated list of triggers
    """

    try:
        # Extract authenticated user ID
        authenticated_user_id = current_user.get("id")
        if not authenticated_user_id:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="User ID not available from authentication",
            )

        # For security, only allow users to see their own triggers unless they're admin
        # For now, we'll restrict to the authenticated user's triggers only
        # TODO: Add admin role checking if needed
        filter_user_id = authenticated_user_id

        # Use the new list_triggers method with database-level pagination
        triggers, total_count = await trigger_manager.list_triggers(
            user_id=filter_user_id,
            workflow_id=workflow_id,
            trigger_type=trigger_type,
            is_active=is_active,
            page=page,
            page_size=page_size,
        )

        # Convert to response models
        trigger_responses = [TriggerResponse.from_orm(t) for t in triggers]

        # Calculate total pages
        total_pages = (total_count + page_size - 1) // page_size

        return TriggerListResponse(
            triggers=trigger_responses,
            total=total_count,
            page=page,
            page_size=page_size,
            total_pages=total_pages,
        )

    except HTTPException:
        raise
    except Exception as e:

        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error while listing triggers",
        )


@router.get("/{trigger_id}", response_model=TriggerResponse)
async def get_trigger(
    trigger_id: UUID,
    current_user: Dict[str, Any] = Depends(get_authenticated_user),
    trigger_manager: TriggerManager = Depends(get_trigger_manager),
) -> TriggerResponse:
    """
    Get a specific trigger by ID.

    Args:
        trigger_id: Trigger ID to retrieve
        current_user: Authenticated user information from bearer token
        trigger_manager: Trigger manager instance

    Returns:
        TriggerResponse: Trigger details

    Raises:
        HTTPException: If trigger not found or access denied
    """

    try:
        # Extract authenticated user ID
        authenticated_user_id = current_user.get("id")
        if not authenticated_user_id:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="User ID not available from authentication",
            )

        # Get trigger by ID directly
        trigger = await trigger_manager.get_trigger_by_id(str(trigger_id))

        if not trigger:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="Trigger not found"
            )

        # Check if the trigger belongs to the authenticated user
        if trigger.user_id != authenticated_user_id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Access denied: You can only access your own triggers",
            )

        return TriggerResponse.from_orm(trigger)

    except HTTPException:
        raise
    except Exception as e:

        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error while retrieving trigger",
        )


@router.put("/{trigger_id}", response_model=TriggerResponse)
async def update_trigger(
    trigger_id: UUID,
    trigger_data: TriggerUpdate,
    current_user: Dict[str, Any] = Depends(get_authenticated_user),
    trigger_manager: TriggerManager = Depends(get_trigger_manager),
) -> TriggerResponse:
    """
    Update an existing trigger.

    This endpoint allows you to update various aspects of a trigger including:
    - Trigger name (must be unique within the workflow)
    - Configuration settings (adapter-specific)
    - Event types to monitor
    - Input values for template processing
    - Active/inactive status

    **Important Notes:**
    - All fields are optional - only provided fields will be updated
    - Trigger names must be unique within each workflow for the same user
    - Multiple triggers of the same type can exist for one workflow (with different names)
    - Changing configuration or event types will re-register the trigger with the adapter
    - Changing the active status will pause/resume the trigger
    - You can only update triggers that belong to your user account
    - workflow_id and trigger_type cannot be changed after creation

    **Example Request Body (Update Name and Status):**
    ```json
    {
      "trigger_name": "Updated Calendar Trigger",
      "is_active": false
    }
    ```

    **Example Request Body (Update Configuration and Input Values):**
    ```json
    {
      "trigger_config": {
        "calendar_id": "primary",
        "use_polling": false,
        "webhook_ttl": 604800
      },
      "event_types": ["created", "updated"],
      "input_values": [
        {
          "field_name": "body",
          "field_value": "Meeting '{json.summary}' starts at {json.start.dateTime}",
          "field_type": "string"
        }
      ]
    }
    ```

    Args:
        trigger_id: UUID of the trigger to update
        trigger_data: Updated trigger data (all fields optional)
        current_user: Authenticated user information from bearer token
        trigger_manager: Trigger manager instance

    Returns:
        TriggerResponse: Updated trigger details

    Raises:
        HTTPException:
            - 404 if trigger not found
            - 403 if trigger belongs to another user
            - 400 if update fails due to invalid data
            - 500 if internal server error occurs
    """

    try:
        # Extract authenticated user ID
        authenticated_user_id = current_user.get("id")
        if not authenticated_user_id:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="User ID not available from authentication",
            )

        # Get trigger by ID directly
        trigger = await trigger_manager.get_trigger_by_id(str(trigger_id))

        if not trigger:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="Trigger not found"
            )

        # Check if the trigger belongs to the authenticated user
        if trigger.user_id != authenticated_user_id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Access denied: You can only update your own triggers",
            )

        # Validate that at least one field is being updated
        if not any(
            [
                trigger_data.trigger_name is not None,
                trigger_data.trigger_config is not None,
                trigger_data.event_types is not None,
                trigger_data.input_values is not None,
                trigger_data.is_active is not None,
            ]
        ):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="At least one field must be provided for update",
            )

        # Validate event types if provided
        if trigger_data.event_types is not None:
            valid_event_types = {"created", "updated", "deleted", "reminder"}
            invalid_types = set(trigger_data.event_types) - valid_event_types
            if invalid_types:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"Invalid event types: {', '.join(invalid_types)}. "
                    f"Valid types are: {', '.join(valid_event_types)}",
                )

        # Update the trigger using TriggerManager
        updated_trigger = await trigger_manager.update_trigger(trigger_id, trigger_data)

        if not updated_trigger:
            # Check if it's a constraint violation by trying to determine the cause
            if trigger_data.trigger_name is not None:
                # Check if trigger name already exists for this user and workflow
                existing_triggers = await trigger_manager.get_triggers_for_user(
                    authenticated_user_id
                )
                name_conflict = any(
                    t.trigger_name == trigger_data.trigger_name
                    and t.workflow_id == trigger.workflow_id
                    and t.id != trigger_id
                    for t in existing_triggers
                )

                if name_conflict:
                    raise HTTPException(
                        status_code=status.HTTP_409_CONFLICT,
                        detail=f"A trigger with the name '{trigger_data.trigger_name}' already exists for this workflow. "
                        "Trigger names must be unique within each workflow.",
                    )

            # Generic failure message for other cases
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Failed to update trigger. This may be due to invalid configuration "
                "or adapter registration failure.",
            )

        logger.info(
            f"Successfully updated trigger {trigger_id}",
            user_id=authenticated_user_id,
            trigger_name=updated_trigger.trigger_name,
            updated_fields=[
                field
                for field, value in [
                    ("trigger_name", trigger_data.trigger_name),
                    ("trigger_config", trigger_data.trigger_config),
                    ("event_types", trigger_data.event_types),
                    ("input_values", trigger_data.input_values),
                    ("is_active", trigger_data.is_active),
                ]
                if value is not None
            ],
        )

        return TriggerResponse.from_orm(updated_trigger)

    except HTTPException:
        raise
    except Exception as e:

        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error while updating trigger",
        )


@router.delete("/{trigger_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_trigger(
    trigger_id: UUID,
    current_user: Dict[str, Any] = Depends(get_authenticated_user),
    trigger_manager: TriggerManager = Depends(get_trigger_manager),
) -> None:
    """
    Delete a trigger.

    Args:
        trigger_id: Trigger ID to delete
        current_user: Authenticated user information from bearer token
        trigger_manager: Trigger manager instance

    Raises:
        HTTPException: If trigger not found or deletion fails
    """

    try:
        # Extract authenticated user ID
        authenticated_user_id = current_user.get("id")
        if not authenticated_user_id:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="User ID not available from authentication",
            )

        # Get trigger by ID directly
        trigger = await trigger_manager.get_trigger_by_id(str(trigger_id))

        if not trigger:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="Trigger not found"
            )

        # Check if the trigger belongs to the authenticated user
        if trigger.user_id != authenticated_user_id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Access denied: You can only delete your own triggers",
            )

        # Delete the trigger
        success = await trigger_manager.remove_trigger(trigger_id)
        if not success:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to delete trigger",
            )

    except HTTPException:
        raise
    except Exception as e:

        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error while deleting trigger",
        )


@router.post("/{trigger_id}/toggle", response_model=TriggerResponse)
async def toggle_trigger(
    trigger_id: UUID,
    toggle_data: TriggerToggleRequest,
    current_user: Dict[str, Any] = Depends(get_authenticated_user),
    trigger_manager: TriggerManager = Depends(get_trigger_manager),
) -> TriggerResponse:
    """
    Enable or disable a trigger.

    Args:
        trigger_id: Trigger ID to toggle
        toggle_data: Toggle request data
        current_user: Authenticated user information from bearer token
        trigger_manager: Trigger manager instance

    Returns:
        TriggerResponse: Updated trigger details

    Raises:
        HTTPException: If trigger not found or toggle fails
    """

    try:
        # Extract authenticated user ID
        authenticated_user_id = current_user.get("id")
        if not authenticated_user_id:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="User ID not available from authentication",
            )

        # Get trigger by ID directly
        trigger = await trigger_manager.get_trigger_by_id(str(trigger_id))

        if not trigger:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="Trigger not found"
            )

        # Check if the trigger belongs to the authenticated user
        if trigger.user_id != authenticated_user_id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Access denied: You can only toggle your own triggers",
            )

        # Toggle the trigger
        success = await trigger_manager.toggle_trigger(
            trigger_id, toggle_data.is_active
        )
        if not success:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to toggle trigger",
            )

        # Get updated trigger details
        updated_trigger = await trigger_manager.get_trigger_by_id(str(trigger_id))

        if not updated_trigger:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Trigger toggled but could not retrieve updated details",
            )

        action = "enabled" if toggle_data.is_active else "disabled"

        return TriggerResponse.from_orm(updated_trigger)

    except HTTPException:
        raise
    except Exception as e:

        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error while toggling trigger",
        )


@router.get("/{trigger_id}/executions", response_model=List[TriggerExecutionResponse])
async def get_trigger_executions(
    trigger_id: UUID,
    current_user: Dict[str, Any] = Depends(get_authenticated_user),
    limit: int = Query(
        100, ge=1, le=1000, description="Maximum number of executions to return"
    ),
    trigger_manager: TriggerManager = Depends(get_trigger_manager),
) -> List[TriggerExecutionResponse]:
    """
    Get execution history for a trigger.

    Args:
        trigger_id: Trigger ID to get executions for
        current_user: Authenticated user information from bearer token
        limit: Maximum number of executions to return
        trigger_manager: Trigger manager instance

    Returns:
        List[TriggerExecutionResponse]: List of trigger executions

    Raises:
        HTTPException: If trigger not found or access denied
    """

    try:
        # Extract authenticated user ID
        authenticated_user_id = current_user.get("id")
        if not authenticated_user_id:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="User ID not available from authentication",
            )

        # Get trigger by ID directly
        trigger = await trigger_manager.get_trigger_by_id(str(trigger_id))

        if not trigger:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="Trigger not found"
            )

        # Check if the trigger belongs to the authenticated user
        if trigger.user_id != authenticated_user_id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Access denied: You can only view executions for your own triggers",
            )

        # Get execution history
        executions = await trigger_manager.get_execution_history(trigger_id, limit)

        return [
            TriggerExecutionResponse.from_orm(execution) for execution in executions
        ]

    except HTTPException:
        raise
    except Exception as e:

        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error while retrieving trigger executions",
        )


# @router.get("/stats", response_model=TriggerStatsResponse)
# async def get_trigger_stats(
#     request: Request, trigger_manager: TriggerManager = Depends(get_trigger_manager)
# ) -> TriggerStatsResponse:
#     """
#     Get trigger statistics for the current user.

#     Args:
#         request: HTTP request for authentication
#         trigger_manager: Trigger manager instance

#     Returns:
#         TriggerStatsResponse: Trigger statistics
#     """
#     try:
#         # Get user's triggers
#         triggers = await trigger_manager.get_triggers_for_user(current_user)

#         # Calculate statistics
#         total_triggers = len(triggers)
#         active_triggers = len([t for t in triggers if t.is_active])
#         inactive_triggers = total_triggers - active_triggers

#         # Count by type
#         triggers_by_type = {}
#         for trigger in triggers:
#             trigger_type = trigger.trigger_type
#             triggers_by_type[trigger_type] = triggers_by_type.get(trigger_type, 0) + 1

#         # For now, return placeholder values for execution stats
#         # In a full implementation, you'd query the execution history
#         recent_executions = 0
#         success_rate = 100.0

#         return TriggerStatsResponse(
#             total_triggers=total_triggers,
#             active_triggers=active_triggers,
#             inactive_triggers=inactive_triggers,
#             triggers_by_type=triggers_by_type,
#             recent_executions=recent_executions,
#             success_rate=success_rate,
#         )

#     except HTTPException:
#         raise
#     except Exception as e:
#         logger.error(
#             f"Failed to get trigger stats for user {current_user}", error=str(e)
#         )
#         raise HTTPException(
#             status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
#             detail="Internal server error while retrieving trigger statistics",
#         )
