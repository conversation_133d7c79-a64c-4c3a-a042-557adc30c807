"""
Enhanced Health Check API for Load Balancer Integration.

Provides comprehensive health checks including:
- Basic service health
- Database connectivity
- Redis connectivity
- Instance coordination status
- Load metrics
"""

import asyncio
import time
from datetime import datetime, timezone
from typing import Dict, Any, Optional

from fastapi import APIRouter, HTTPException, status, Depends
from sqlalchemy import text
from sqlalchemy.ext.asyncio import AsyncSession

from src.core.scheduler_service import SchedulerService
from src.database.connection import get_async_session
from src.utils.config import get_settings
from src.utils.logger import get_logger

logger = get_logger(__name__)
router = APIRouter(prefix="/health", tags=["health"])

# Global reference to scheduler service for health checks
_scheduler_service: Optional[SchedulerService] = None


def set_scheduler_service(service: SchedulerService):
    """Set the global scheduler service reference for health checks."""
    global _scheduler_service
    _scheduler_service = service


@router.get("/")
async def basic_health():
    """Basic health check for load balancer."""
    return {
        "status": "healthy",
        "timestamp": datetime.now(timezone.utc).isoformat(),
        "service": "workflow-automation-platform"
    }


@router.get("/ready")
async def readiness_check(db: AsyncSession = Depends(get_async_session)):
    """
    Readiness check for Kubernetes/container orchestration.
    Returns 200 if service is ready to accept traffic.
    """
    try:
        # Check database connectivity
        await db.execute(text("SELECT 1"))
        
        # Check if scheduler service is running
        if _scheduler_service and not _scheduler_service.running:
            raise HTTPException(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                detail="Scheduler service not running"
            )
        
        return {
            "status": "ready",
            "timestamp": datetime.now(timezone.utc).isoformat()
        }
    
    except Exception as e:
        logger.error(f"Readiness check failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail=f"Service not ready: {str(e)}"
        )


@router.get("/live")
async def liveness_check():
    """
    Liveness check for Kubernetes/container orchestration.
    Returns 200 if service is alive (should restart if this fails).
    """
    try:
        # Basic process health check
        current_time = time.time()
        
        return {
            "status": "alive",
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "uptime_seconds": current_time - (getattr(_scheduler_service, 'start_time', current_time))
        }
    
    except Exception as e:
        logger.error(f"Liveness check failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail=f"Service not alive: {str(e)}"
        )


@router.get("/detailed")
async def detailed_health(db: AsyncSession = Depends(get_async_session)):
    """
    Detailed health check including all dependencies and metrics.
    Used for monitoring and debugging.
    """
    health_data = {
        "status": "healthy",
        "timestamp": datetime.now(timezone.utc).isoformat(),
        "checks": {}
    }
    
    overall_healthy = True
    
    # Database check
    try:
        start_time = time.time()
        result = await db.execute(text("SELECT version()"))
        db_version = result.scalar()
        db_response_time = time.time() - start_time
        
        health_data["checks"]["database"] = {
            "status": "healthy",
            "response_time_ms": round(db_response_time * 1000, 2),
            "version": db_version
        }
    except Exception as e:
        health_data["checks"]["database"] = {
            "status": "unhealthy",
            "error": str(e)
        }
        overall_healthy = False
    
    # Redis check (if available)
    if _scheduler_service and _scheduler_service.lock_manager:
        try:
            # Test Redis connectivity through lock manager
            test_key = f"health_check_{int(time.time())}"
            start_time = time.time()
            
            if hasattr(_scheduler_service.lock_manager.primary_lock, 'redis_client'):
                redis_client = _scheduler_service.lock_manager.primary_lock.redis_client
                await redis_client.ping()
                redis_response_time = time.time() - start_time
                
                # Get Redis info
                redis_info = await redis_client.info()
                
                health_data["checks"]["redis"] = {
                    "status": "healthy",
                    "response_time_ms": round(redis_response_time * 1000, 2),
                    "version": redis_info.get("redis_version", "unknown"),
                    "connected_clients": redis_info.get("connected_clients", 0),
                    "used_memory_human": redis_info.get("used_memory_human", "unknown")
                }
            else:
                health_data["checks"]["redis"] = {
                    "status": "not_configured",
                    "message": "Using database-only mode"
                }
        except Exception as e:
            health_data["checks"]["redis"] = {
                "status": "unhealthy",
                "error": str(e)
            }
            # Redis failure is not critical if we have database fallback
            logger.warning(f"Redis health check failed: {e}")
    
    # Scheduler service check
    if _scheduler_service:
        try:
            health_data["checks"]["scheduler_service"] = {
                "status": "healthy" if _scheduler_service.running else "stopped",
                "batch_size": _scheduler_service.batch_size,
                "max_concurrent_schedulers": _scheduler_service.max_concurrent_schedulers,
                "worker_concurrency": _scheduler_service.worker_concurrency
            }
            
            # Add metrics if available
            if hasattr(_scheduler_service.scheduler_engine, 'metrics'):
                metrics = _scheduler_service.scheduler_engine.metrics
                health_data["checks"]["scheduler_service"]["metrics"] = {
                    "total_processed": metrics.total_schedulers_processed,
                    "total_queued": metrics.total_schedulers_queued,
                    "success_rate": round(metrics.success_rate, 2),
                    "engine_errors": metrics.engine_errors
                }
        except Exception as e:
            health_data["checks"]["scheduler_service"] = {
                "status": "unhealthy",
                "error": str(e)
            }
            overall_healthy = False
    else:
        health_data["checks"]["scheduler_service"] = {
            "status": "not_initialized"
        }
        overall_healthy = False
    
    # Instance coordination check
    if _scheduler_service and _scheduler_service.instance_coordinator:
        try:
            coordinator = _scheduler_service.instance_coordinator
            active_instances = await coordinator.get_active_instances()
            
            health_data["checks"]["instance_coordination"] = {
                "status": "healthy",
                "instance_id": coordinator.instance_id,
                "active_instances_count": len(active_instances),
                "active_instances": [
                    {
                        "id": inst.instance_id,
                        "hostname": inst.hostname,
                        "port": inst.port,
                        "load": inst.current_load,
                        "status": inst.status
                    }
                    for inst in active_instances
                ]
            }
        except Exception as e:
            health_data["checks"]["instance_coordination"] = {
                "status": "unhealthy",
                "error": str(e)
            }
            # Instance coordination failure is not critical for single instance
            logger.warning(f"Instance coordination health check failed: {e}")
    
    # Task queue check (if available)
    if _scheduler_service and _scheduler_service.task_queue:
        try:
            # Check task queue health
            health_data["checks"]["task_queue"] = {
                "status": "healthy",
                "type": "redis"
            }
            
            # Add task worker metrics if available
            if _scheduler_service.task_worker:
                health_data["checks"]["task_queue"]["worker_status"] = "running"
        except Exception as e:
            health_data["checks"]["task_queue"] = {
                "status": "unhealthy",
                "error": str(e)
            }
            overall_healthy = False
    
    # Set overall status
    health_data["status"] = "healthy" if overall_healthy else "degraded"
    
    # Return appropriate HTTP status
    if not overall_healthy:
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail=health_data
        )
    
    return health_data


@router.get("/metrics")
async def health_metrics():
    """
    Lightweight metrics endpoint for load balancer health scoring.
    Returns key performance indicators for load balancing decisions.
    """
    if not _scheduler_service:
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail="Scheduler service not available"
        )
    
    metrics_data = {
        "timestamp": datetime.now(timezone.utc).isoformat(),
        "instance_id": getattr(_scheduler_service.instance_coordinator, 'instance_id', 'unknown'),
        "load_score": 0.0,  # 0.0 = no load, 1.0 = maximum load
        "healthy": _scheduler_service.running
    }
    
    try:
        # Calculate load score based on various factors
        load_factors = []
        
        # Scheduler engine metrics
        if hasattr(_scheduler_service.scheduler_engine, 'metrics'):
            engine_metrics = _scheduler_service.scheduler_engine.metrics
            
            # Factor 1: Error rate (higher errors = higher load score)
            if engine_metrics.total_schedulers_processed > 0:
                error_rate = 1.0 - (engine_metrics.success_rate / 100.0)
                load_factors.append(error_rate * 0.3)  # 30% weight
            
            # Factor 2: Processing volume (more processing = higher load)
            # Normalize based on batch size
            processing_load = min(1.0, engine_metrics.total_schedulers_processed / (_scheduler_service.batch_size * 10))
            load_factors.append(processing_load * 0.4)  # 40% weight
        
        # Factor 3: Instance coordination load (if available)
        if _scheduler_service.instance_coordinator:
            try:
                active_instances = await _scheduler_service.instance_coordinator.get_active_instances()
                current_instance = next(
                    (inst for inst in active_instances 
                     if inst.instance_id == _scheduler_service.instance_coordinator.instance_id),
                    None
                )
                if current_instance:
                    load_factors.append(current_instance.current_load * 0.3)  # 30% weight
            except Exception:
                pass  # Ignore coordination errors for metrics
        
        # Calculate overall load score
        if load_factors:
            metrics_data["load_score"] = min(1.0, sum(load_factors))
        
        # Add detailed metrics
        if hasattr(_scheduler_service.scheduler_engine, 'metrics'):
            engine_metrics = _scheduler_service.scheduler_engine.metrics
            metrics_data["detailed"] = {
                "schedulers_processed": engine_metrics.total_schedulers_processed,
                "schedulers_queued": engine_metrics.total_schedulers_queued,
                "success_rate": engine_metrics.success_rate,
                "engine_errors": engine_metrics.engine_errors
            }
    
    except Exception as e:
        logger.error(f"Error calculating health metrics: {e}")
        metrics_data["load_score"] = 1.0  # Assume high load on error
        metrics_data["error"] = str(e)
    
    return metrics_data
