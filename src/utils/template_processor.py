"""
Template processing utilities for dynamic input values.

This module provides template processing functionality that allows dynamic substitution
of event data in input values using {json.field} syntax.
"""

import re
from typing import Dict, Any, List, Optional, Union
import structlog

logger = structlog.get_logger(__name__)


class TemplateProcessor:
    """Template processor for dynamic input value substitution."""
    
    # Regex pattern to match {json.field} syntax
    TEMPLATE_PATTERN = re.compile(r'\{json\.([^}]+)\}')
    
    @staticmethod
    def has_template_syntax(value: str) -> bool:
        """
        Check if a string contains template syntax.
        
        Args:
            value: String to check
            
        Returns:
            bool: True if the string contains {json.field} syntax
        """
        if not isinstance(value, str):
            return False
        return bool(TemplateProcessor.TEMPLATE_PATTERN.search(value))
    
    @staticmethod
    def extract_event_data(event_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Extract the appropriate event data for template processing.
        
        Args:
            event_data: Raw event data from trigger
            
        Returns:
            Dict[str, Any]: Extracted event data suitable for template processing
        """
        # Handle different event data structures
        if isinstance(event_data, list) and len(event_data) > 0:
            # Array format - use first element
            event_data = event_data[0]
        
        if not isinstance(event_data, dict):
            return {}
        
        # Try to extract specific event data based on adapter type
        data_section = event_data.get("data", {})
        
        # Google Calendar events
        if "calendar_event" in data_section:
            return data_section["calendar_event"]
        
        # Google Drive events
        if "file_event" in data_section:
            return data_section["file_event"]
        
        # Generic webhook events
        if "webhook_event" in data_section:
            return data_section["webhook_event"]
        
        # Generic event data
        if "event" in data_section:
            return data_section["event"]
        
        # Fallback to data section
        if data_section:
            return data_section
        
        # Final fallback to entire event data
        return event_data
    
    @staticmethod
    def get_nested_field_value(data: Dict[str, Any], field_path: str) -> Any:
        """
        Get value from nested dictionary using dot notation.
        
        Args:
            data: Dictionary to extract from
            field_path: Dot-separated path (e.g., 'start.dateTime')
            
        Returns:
            Any: Field value or None if not found
        """
        try:
            current = data
            for key in field_path.split("."):
                if isinstance(current, dict) and key in current:
                    current = current[key]
                else:
                    return None
            return current
        except (KeyError, TypeError, AttributeError):
            return None
    
    @staticmethod
    def process_template(
        template_value: str, 
        event_data: Dict[str, Any]
    ) -> Union[str, None]:
        """
        Process a template string and substitute {json.field} references.
        
        Args:
            template_value: Template string with {json.field} syntax
            event_data: Event data for substitution
            
        Returns:
            Union[str, None]: Processed string or None if single field reference not found
        """
        if not isinstance(template_value, str):
            return template_value
        
        # Find all template references
        matches = TemplateProcessor.TEMPLATE_PATTERN.findall(template_value)
        
        if not matches:
            # No template syntax, return as-is
            return template_value
        
        # Check if this is a single field reference (entire value is {json.field})
        single_field_match = re.match(r'^\{json\.([^}]+)\}$', template_value)
        if single_field_match:
            field_path = single_field_match.group(1)
            field_value = TemplateProcessor.get_nested_field_value(event_data, field_path)
            return field_value  # Return None if field not found
        
        # Multiple references or mixed content - substitute each reference
        result = template_value
        for field_path in matches:
            field_value = TemplateProcessor.get_nested_field_value(event_data, field_path)
            placeholder = f"{{json.{field_path}}}"
            
            # Replace with field value or empty string if not found
            replacement = str(field_value) if field_value is not None else ""
            result = result.replace(placeholder, replacement)
        
        return result
    
    @staticmethod
    def process_input_values(
        input_values: List[Dict[str, Any]],
        event_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Process input values with template substitution.
        
        Args:
            input_values: List of input value configurations
            event_data: Event data for template processing
            
        Returns:
            Dict[str, Any]: Processed input values with templates substituted
        """
        if not input_values:
            return {}
        
        # Extract appropriate event data for template processing
        template_data = TemplateProcessor.extract_event_data(event_data)
        
        processed = {}
        
        for input_value in input_values:
            field_name = input_value.get("field_name")
            field_value = input_value.get("field_value")
            field_type = input_value.get("field_type", "string")
            
            if not field_name or field_value is None:
                continue
            
            # Process template if it's a string
            if isinstance(field_value, str) and TemplateProcessor.has_template_syntax(field_value):
                processed_value = TemplateProcessor.process_template(field_value, template_data)
                
                # Log template processing
                logger.debug(
                    "Processed template",
                    field_name=field_name,
                    template=field_value,
                    result=processed_value,
                    event_data_keys=list(template_data.keys()) if template_data else []
                )
                
                processed[field_name] = processed_value
            else:
                # Static value - use as-is
                processed[field_name] = field_value
        
        return processed


# Convenience instance
template_processor = TemplateProcessor()
