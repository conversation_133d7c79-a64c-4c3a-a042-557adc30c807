"""
Database utility functions for transaction management and error handling.
"""

import asyncio
from typing import Callable, TypeVar, Any
from functools import wraps

from sqlalchemy.exc import (
    DisconnectionError,
    OperationalError,
    InterfaceError,
    DatabaseError,
)
from sqlalchemy.dialects.postgresql.asyncpg import AsyncAdapt_asyncpg_dbapi

from src.utils.logger import get_logger

logger = get_logger(__name__)

T = TypeVar("T")

# Define transient database errors that should be retried
TRANSIENT_ERRORS = (
    DisconnectionError,
    OperationalError,
    InterfaceError,
    AsyncAdapt_asyncpg_dbapi.Error,
)


def is_transient_error(error: Exception) -> bool:
    """
    Check if an error is transient and should be retried.

    Args:
        error: The exception to check

    Returns:
        bool: True if the error is transient and should be retried
    """
    if isinstance(error, TRANSIENT_ERRORS):
        return True

    # Check for specific asyncpg errors
    error_str = str(error).lower()
    transient_messages = [
        "connection is closed",
        "connection was closed",
        "server closed the connection",
        "connection lost",
        "connection reset",
        "connection timeout",
        "current transaction is aborted",
        "deadlock detected",
        "could not serialize access",
    ]

    return any(msg in error_str for msg in transient_messages)


async def retry_db_operation(
    operation: Callable[[], T],
    max_retries: int = 3,
    base_delay: float = 1.0,
    max_delay: float = 10.0,
    backoff_factor: float = 2.0,
) -> T:
    """
    Retry a database operation with exponential backoff.

    Args:
        operation: The async function to retry
        max_retries: Maximum number of retry attempts
        base_delay: Initial delay between retries in seconds
        max_delay: Maximum delay between retries in seconds
        backoff_factor: Factor to multiply delay by after each retry

    Returns:
        The result of the operation

    Raises:
        The last exception if all retries fail
    """
    last_exception = None
    delay = base_delay

    for attempt in range(max_retries + 1):
        try:
            return await operation()
        except Exception as e:
            last_exception = e

            if attempt == max_retries:
                logger.error(
                    f"Database operation failed after {max_retries} retries: {e}",
                    exc_info=True,
                )
                raise

            if not is_transient_error(e):
                logger.error(f"Non-transient database error, not retrying: {e}")
                raise

            logger.warning(
                f"Database operation failed (attempt {attempt + 1}/{max_retries + 1}), "
                f"retrying in {delay:.2f}s: {e}"
            )

            await asyncio.sleep(delay)
            delay = min(delay * backoff_factor, max_delay)

    # This should never be reached, but just in case
    raise last_exception


def with_db_retry(
    max_retries: int = 3,
    base_delay: float = 1.0,
    max_delay: float = 10.0,
    backoff_factor: float = 2.0,
):
    """
    Decorator to add retry logic to database operations.

    Args:
        max_retries: Maximum number of retry attempts
        base_delay: Initial delay between retries in seconds
        max_delay: Maximum delay between retries in seconds
        backoff_factor: Factor to multiply delay by after each retry
    """

    def decorator(func: Callable[..., T]) -> Callable[..., T]:
        @wraps(func)
        async def wrapper(*args, **kwargs) -> T:
            async def operation():
                return await func(*args, **kwargs)

            return await retry_db_operation(
                operation,
                max_retries=max_retries,
                base_delay=base_delay,
                max_delay=max_delay,
                backoff_factor=backoff_factor,
            )

        return wrapper

    return decorator


class TransactionManager:
    """
    Context manager for handling database transactions with proper error handling.
    """

    def __init__(self, session, rollback_on_error: bool = True):
        self.session = session
        self.rollback_on_error = rollback_on_error
        self._transaction = None

    async def __aenter__(self):
        self._transaction = await self.session.begin()
        return self.session

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if exc_type is not None:
            if self.rollback_on_error:
                await self._transaction.rollback()
                logger.debug("Transaction rolled back due to error")
            else:
                await self._transaction.commit()
                logger.debug("Transaction committed despite error")
        else:
            await self._transaction.commit()
            logger.debug("Transaction committed successfully")

        return False  # Don't suppress exceptions
