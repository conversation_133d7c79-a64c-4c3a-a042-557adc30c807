"""
Centralized HTTP client factory for the Trigger Service.

This module provides a centralized way to create and manage HTTP clients
with consistent configuration across the application.
"""

from typing import Dict, Any, Optional
import httpx
from src.utils.config import get_settings


class HTTPClientFactory:
    """Factory for creating configured HTTP clients."""
    
    _default_client: Optional[httpx.AsyncClient] = None
    _auth_client: Optional[httpx.AsyncClient] = None
    _workflow_client: Optional[httpx.AsyncClient] = None
    
    @classmethod
    async def get_default_client(cls) -> httpx.AsyncClient:
        """Get a default HTTP client for general use."""
        if cls._default_client is None:
            cls._default_client = httpx.AsyncClient(
                timeout=httpx.Timeout(30.0),
                limits=httpx.Limits(
                    max_connections=20, 
                    max_keepalive_connections=10
                ),
                headers={
                    "User-Agent": "TriggerService/1.0",
                    "Accept": "application/json",
                }
            )
        return cls._default_client
    
    @classmethod
    async def get_auth_client(cls) -> httpx.AsyncClient:
        """Get an HTTP client configured for auth service."""
        if cls._auth_client is None:
            settings = get_settings()
            cls._auth_client = httpx.AsyncClient(
                timeout=httpx.Timeout(settings.auth_service_timeout),
                limits=httpx.Limits(
                    max_connections=10, 
                    max_keepalive_connections=5
                ),
                headers={
                    "User-Agent": "TriggerService/1.0",
                    "Accept": "application/json",
                }
            )
        return cls._auth_client
    
    @classmethod
    async def get_workflow_client(cls) -> httpx.AsyncClient:
        """Get an HTTP client configured for workflow service."""
        if cls._workflow_client is None:
            settings = get_settings()
            cls._workflow_client = httpx.AsyncClient(
                timeout=httpx.Timeout(30.0),
                limits=httpx.Limits(
                    max_connections=20, 
                    max_keepalive_connections=10
                ),
                headers={
                    "Content-Type": "application/json",
                    "X-Server-Auth-Key": settings.workflow_service_api_key,
                }
            )
        return cls._workflow_client
    
    @classmethod
    async def create_custom_client(
        cls,
        timeout: float = 30.0,
        max_connections: int = 20,
        max_keepalive: int = 10,
        headers: Optional[Dict[str, str]] = None
    ) -> httpx.AsyncClient:
        """Create a custom HTTP client with specified configuration."""
        default_headers = {
            "User-Agent": "TriggerService/1.0",
            "Accept": "application/json",
        }
        
        if headers:
            default_headers.update(headers)
        
        return httpx.AsyncClient(
            timeout=httpx.Timeout(timeout),
            limits=httpx.Limits(
                max_connections=max_connections,
                max_keepalive_connections=max_keepalive
            ),
            headers=default_headers
        )
    
    @classmethod
    async def close_all(cls):
        """Close all cached HTTP clients."""
        clients = [cls._default_client, cls._auth_client, cls._workflow_client]
        for client in clients:
            if client is not None:
                await client.aclose()
        
        cls._default_client = None
        cls._auth_client = None
        cls._workflow_client = None


# Convenience functions
async def get_default_http_client() -> httpx.AsyncClient:
    """Get the default HTTP client."""
    return await HTTPClientFactory.get_default_client()


async def get_auth_http_client() -> httpx.AsyncClient:
    """Get the auth service HTTP client."""
    return await HTTPClientFactory.get_auth_client()


async def get_workflow_http_client() -> httpx.AsyncClient:
    """Get the workflow service HTTP client."""
    return await HTTPClientFactory.get_workflow_client()
