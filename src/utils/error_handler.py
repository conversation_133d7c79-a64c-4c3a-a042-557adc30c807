"""
Centralized error handling utilities for the Trigger Service.

This module provides reusable error handling patterns and utilities
to eliminate code duplication across the application.
"""

import traceback
from typing import Dict, Any, Optional, Callable, TypeVar, Union
from functools import wraps
import structlog
import httpx
from sqlalchemy.exc import SQLAlchemyError

logger = structlog.get_logger(__name__)

T = TypeVar("T")


class ErrorContext:
    """Context information for error handling."""
    
    def __init__(
        self,
        operation: str,
        component: str,
        **context_data: Any
    ):
        self.operation = operation
        self.component = component
        self.context_data = context_data
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for logging."""
        return {
            "operation": self.operation,
            "component": self.component,
            **self.context_data
        }


class ErrorHandler:
    """Centralized error handling utility."""
    
    @staticmethod
    def log_error(
        error: Exception,
        context: ErrorContext,
        include_traceback: bool = False
    ) -> None:
        """
        Log an error with context information.
        
        Args:
            error: Exception that occurred
            context: Error context information
            include_traceback: Whether to include full traceback
        """
        log_data = {
            **context.to_dict(),
            "error_type": type(error).__name__,
            "error_message": str(error),
        }
        
        if include_traceback:
            log_data["traceback"] = traceback.format_exc()
        
        logger.error("Operation failed", **log_data)
    
    @staticmethod
    def log_warning(
        error: Exception,
        context: ErrorContext,
        message: str = "Operation warning"
    ) -> None:
        """
        Log a warning with context information.
        
        Args:
            error: Exception that occurred
            context: Error context information
            message: Warning message
        """
        log_data = {
            **context.to_dict(),
            "error_type": type(error).__name__,
            "error_message": str(error),
        }
        
        logger.warning(message, **log_data)
    
    @staticmethod
    def is_retryable_error(error: Exception) -> bool:
        """
        Check if an error is retryable.
        
        Args:
            error: Exception to check
            
        Returns:
            bool: True if error is retryable
        """
        # HTTP errors that are retryable
        if isinstance(error, httpx.HTTPStatusError):
            return error.response.status_code in [429, 500, 502, 503, 504]
        
        if isinstance(error, (httpx.TimeoutException, httpx.ConnectError)):
            return True
        
        # Database errors that are retryable
        if isinstance(error, SQLAlchemyError):
            error_str = str(error).lower()
            retryable_patterns = [
                "connection is closed",
                "connection was closed",
                "server closed the connection",
                "connection lost",
                "connection reset",
                "connection timeout",
                "deadlock detected",
                "could not serialize access",
            ]
            return any(pattern in error_str for pattern in retryable_patterns)
        
        return False
    
    @staticmethod
    def get_error_category(error: Exception) -> str:
        """
        Get error category for classification.
        
        Args:
            error: Exception to categorize
            
        Returns:
            str: Error category
        """
        if isinstance(error, httpx.HTTPStatusError):
            status_code = error.response.status_code
            if 400 <= status_code < 500:
                return "client_error"
            elif 500 <= status_code < 600:
                return "server_error"
        
        if isinstance(error, (httpx.TimeoutException, httpx.ConnectError)):
            return "network_error"
        
        if isinstance(error, SQLAlchemyError):
            return "database_error"
        
        if isinstance(error, ValueError):
            return "validation_error"
        
        return "unknown_error"


def handle_errors(
    operation: str,
    component: str,
    log_errors: bool = True,
    reraise: bool = True,
    default_return: Any = None,
    **context_data: Any
):
    """
    Decorator for standardized error handling.
    
    Args:
        operation: Operation name for logging
        component: Component name for logging
        log_errors: Whether to log errors
        reraise: Whether to reraise exceptions
        default_return: Default return value on error
        **context_data: Additional context data
    """
    def decorator(func: Callable[..., T]) -> Callable[..., Union[T, Any]]:
        @wraps(func)
        async def async_wrapper(*args, **kwargs) -> Union[T, Any]:
            context = ErrorContext(operation, component, **context_data)
            try:
                return await func(*args, **kwargs)
            except Exception as e:
                if log_errors:
                    ErrorHandler.log_error(e, context)
                
                if reraise:
                    raise
                
                return default_return
        
        @wraps(func)
        def sync_wrapper(*args, **kwargs) -> Union[T, Any]:
            context = ErrorContext(operation, component, **context_data)
            try:
                return func(*args, **kwargs)
            except Exception as e:
                if log_errors:
                    ErrorHandler.log_error(e, context)
                
                if reraise:
                    raise
                
                return default_return
        
        # Return appropriate wrapper based on function type
        import asyncio
        if asyncio.iscoroutinefunction(func):
            return async_wrapper
        else:
            return sync_wrapper
    
    return decorator


def safe_execute(
    func: Callable[..., T],
    *args,
    context: ErrorContext,
    log_errors: bool = True,
    default_return: Any = None,
    **kwargs
) -> Union[T, Any]:
    """
    Safely execute a function with error handling.
    
    Args:
        func: Function to execute
        *args: Function arguments
        context: Error context
        log_errors: Whether to log errors
        default_return: Default return value on error
        **kwargs: Function keyword arguments
        
    Returns:
        Function result or default_return on error
    """
    try:
        return func(*args, **kwargs)
    except Exception as e:
        if log_errors:
            ErrorHandler.log_error(e, context)
        return default_return


async def safe_execute_async(
    func: Callable[..., T],
    *args,
    context: ErrorContext,
    log_errors: bool = True,
    default_return: Any = None,
    **kwargs
) -> Union[T, Any]:
    """
    Safely execute an async function with error handling.
    
    Args:
        func: Async function to execute
        *args: Function arguments
        context: Error context
        log_errors: Whether to log errors
        default_return: Default return value on error
        **kwargs: Function keyword arguments
        
    Returns:
        Function result or default_return on error
    """
    try:
        return await func(*args, **kwargs)
    except Exception as e:
        if log_errors:
            ErrorHandler.log_error(e, context)
        return default_return


# Convenience instances
error_handler = ErrorHandler()
