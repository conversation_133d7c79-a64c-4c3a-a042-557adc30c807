"""
Centralized field conversion utilities for the Trigger Service.

This module provides reusable field conversion and extraction utilities
to eliminate code duplication across the application.
"""

from typing import Dict, Any, List, Optional, Union
import structlog

logger = structlog.get_logger(__name__)


class FieldConverter:
    """Utility class for field type conversion and validation."""

    @staticmethod
    def convert_field_value(
        value: Any, field_type: str, field_name: Optional[str] = None
    ) -> Any:
        """
        Convert a field value to the specified type.

        Args:
            value: Value to convert
            field_type: Target type ('string', 'number', 'boolean', 'array')
            field_name: Optional field name for logging

        Returns:
            Any: Converted value or original value if conversion fails
        """
        if value is None:
            return None

        try:
            if field_type == "number":
                if isinstance(value, (str, int, float)):
                    return float(value)
                return value

            elif field_type == "boolean":
                if isinstance(value, str):
                    return value.lower() in ("true", "1", "yes", "on")
                return bool(value)

            elif field_type == "string":
                return str(value)

            elif field_type == "array":
                if isinstance(value, list):
                    return value
                elif isinstance(value, str):
                    # Try to parse as comma-separated values
                    return [item.strip() for item in value.split(",")]
                return [value]

            else:
                # Unknown type, return as string
                return str(value)

        except (ValueError, TypeError) as e:
            logger.warning(
                "Field conversion failed",
                field_name=field_name,
                field_type=field_type,
                value=value,
                error=str(e),
            )
            return value

    @staticmethod
    def get_default_value_for_type(field_type: str, field_name: str) -> Any:
        """
        Get a default value for a field type.

        Args:
            field_type: Field type
            field_name: Field name for generating meaningful defaults

        Returns:
            Any: Default value for the type
        """
        if field_type == "string":
            return f"trigger_event_{field_name}"
        elif field_type == "number":
            return 0
        elif field_type == "boolean":
            return False
        elif field_type == "array":
            return []
        else:
            return f"trigger_event_{field_name}"


class FieldExtractor:
    """Utility class for extracting fields from various data sources."""

    @staticmethod
    def get_nested_field_value(data: Dict[str, Any], field_path: str) -> Any:
        """
        Get value from nested dictionary using dot notation.

        Args:
            data: Dictionary to extract from
            field_path: Dot-separated path (e.g., 'start.dateTime')

        Returns:
            Any: Field value or None if not found
        """
        try:
            current = data
            for key in field_path.split("."):
                if isinstance(current, dict) and key in current:
                    current = current[key]
                else:
                    return None
            return current
        except (KeyError, TypeError, AttributeError):
            return None

    @staticmethod
    def extract_mapped_fields(
        source_data: Dict[str, Any],
        field_mappings: List[Dict[str, Any]],
        converter: Optional[FieldConverter] = None,
    ) -> Dict[str, Any]:
        """
        Extract fields from source data using field mappings.

        Args:
            source_data: Source data dictionary
            field_mappings: List of field mapping configurations
            converter: Optional field converter instance

        Returns:
            Dict[str, Any]: Extracted and converted fields
        """
        if converter is None:
            converter = FieldConverter()

        extracted = {}

        for mapping in field_mappings:
            source_field = mapping.get("source_field") or mapping.get("calendar_field")
            target_field = mapping.get("target_field") or mapping.get("workflow_field")
            field_type = mapping.get("field_type", "string")

            if not source_field or not target_field:
                continue

            # Extract value using dot notation for nested fields
            field_value = FieldExtractor.get_nested_field_value(
                source_data, source_field
            )

            # Convert value if found
            if field_value is not None:
                converted_value = converter.convert_field_value(
                    field_value, field_type, target_field
                )
                extracted[target_field] = converted_value

        return extracted

    @staticmethod
    def extract_input_values(
        input_values: List[Dict[str, Any]],
        converter: Optional[FieldConverter] = None,
        event_data: Optional[Dict[str, Any]] = None,
    ) -> Dict[str, Any]:
        """
        Extract input values from configuration with template processing support.

        Args:
            input_values: List of input value configurations
            converter: Optional field converter instance
            event_data: Optional event data for template processing

        Returns:
            Dict[str, Any]: Extracted and converted values
        """
        if converter is None:
            converter = FieldConverter()

        extracted = {}

        if not input_values:
            return extracted

        # If event_data is provided, use template processing
        if event_data:
            from .template_processor import template_processor

            processed_values = template_processor.process_input_values(
                input_values, event_data
            )

            # Apply type conversion to processed values
            for field_name, field_value in processed_values.items():
                # Find the field_type from original input_values
                field_type = "string"  # default
                for input_value in input_values:
                    if input_value.get("field_name") == field_name:
                        field_type = input_value.get("field_type", "string")
                        break

                converted_value = converter.convert_field_value(
                    field_value, field_type, field_name
                )
                extracted[field_name] = converted_value
        else:
            # Legacy behavior - no template processing
            for input_value in input_values:
                field_name = input_value.get("field_name")
                field_value = input_value.get("field_value")
                field_type = input_value.get("field_type", "string")

                if not field_name or field_value is None:
                    continue

                # Convert value
                converted_value = converter.convert_field_value(
                    field_value, field_type, field_name
                )
                extracted[field_name] = converted_value

        return extracted

    @staticmethod
    def extract_basic_event_fields(event_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Extract basic event fields from event data.

        Args:
            event_data: Event data dictionary

        Returns:
            Dict[str, Any]: Basic extracted fields
        """
        extracted = {
            "event_type": event_data.get("event_type"),
            "event_id": event_data.get("event_id"),
            "timestamp": event_data.get("timestamp"),
            "source": event_data.get("source"),
        }

        # Add adapter-specific data
        if "data" in event_data:
            extracted.update(event_data["data"])

        return extracted


# Convenience instances
field_converter = FieldConverter()
field_extractor = FieldExtractor()
