"""
Trigger Manager - Core orchestration for trigger lifecycle management.

This module provides the main TriggerManager class that coordinates trigger
detection, adapter management, and workflow execution.
"""

from typing import Dict, List, Optional, Any, Tuple
from uuid import UUID
import asyncio
from datetime import datetime

from sqlalchemy import select, and_, desc, func
from sqlalchemy.orm import selectinload
from sqlalchemy.exc import IntegrityError

from src.adapters.base import (
    BaseTriggerAdapter,
    TriggerEvent,
    TriggerConfiguration,
    TriggerEventType,
    AdapterHealthStatus,
)
from src.database.models import Trigger, TriggerExecution
from src.database.connection import get_async_session, get_db_manager
from src.core.workflow_executor import WorkflowExecutor
from src.utils.logger import get_logger
from src.utils.retry import RetryHandler
from src.schemas.trigger import TriggerCreate, TriggerUpdate
from sqlalchemy.ext.asyncio import AsyncSession

logger = get_logger(__name__)


class TriggerManager:
    """
    Central manager for trigger lifecycle and event processing.

    The TriggerManager coordinates between different trigger adapters,
    manages trigger configurations, and orchestrates workflow execution
    when triggers are activated.
    """

    _instance = None

    def __new__(cls):
        """Ensure singleton pattern for TriggerManager."""
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance

    @classmethod
    def get_instance(cls):
        """Get the singleton instance of TriggerManager."""
        if cls._instance is None:
            cls._instance = cls()
        return cls._instance

    def __init__(self):
        """Initialize the trigger manager."""
        # Only initialize once (singleton pattern)
        if hasattr(self, "_initialized"):
            return

        self._adapters: Dict[str, BaseTriggerAdapter] = {}
        self._workflow_executor = WorkflowExecutor()
        self._retry_handler = RetryHandler()
        self.db_manager = get_db_manager()
        self._initialized = True
        logger.info("TriggerManager initialized")

        # Register default adapters
        self._register_default_adapters()

    def _register_default_adapters(self):
        """Register default adapters with the trigger manager."""
        try:
            # Register Google Calendar adapter
            from src.adapters.google_calendar import GoogleCalendarAdapter

            google_calendar_adapter = GoogleCalendarAdapter.get_instance()
            self.register_adapter(google_calendar_adapter)

            # Register Google Drive adapter
            from src.adapters.google_drive_user import GoogleDriveUserAdapter

            google_drive_adapter = GoogleDriveUserAdapter.get_instance()
            self.register_adapter(google_drive_adapter)

            logger.info("Successfully registered default adapters")

        except Exception as e:
            logger.error(f"Failed to register default adapters: {e}")

    def register_adapter(self, adapter: BaseTriggerAdapter) -> None:
        """
        Register a trigger adapter with the manager.

        Args:
            adapter: The adapter instance to register
        """
        self._adapters[adapter.adapter_name] = adapter
        logger.info("Registered adapter", adapter_name=adapter.adapter_name)

    def get_adapter(self, adapter_name: str) -> Optional[BaseTriggerAdapter]:
        """
        Get a registered adapter by name.

        Args:
            adapter_name: Name of the adapter to retrieve

        Returns:
            BaseTriggerAdapter: The adapter instance, or None if not found
        """
        return self._adapters.get(adapter_name)

    def list_adapters(self) -> List[str]:
        """
        Get list of registered adapter names.

        Returns:
            List[str]: List of adapter names
        """
        return list(self._adapters.keys())

    async def create_trigger(
        self,
        trigger_data: TriggerCreate,
        session: AsyncSession,
    ) -> Optional[Trigger]:
        """
        Create a new trigger from TriggerCreate schema object.

        Args:
            trigger_data: TriggerCreate object with trigger configuration
            session: Database session to use

        Returns:
            Trigger: Created trigger object if successful, None otherwise
        """
        try:
            adapter = self.get_adapter(trigger_data.trigger_type)
            if not adapter:
                logger.error(f"Adapter not found: {trigger_data.trigger_type}")
                return None

            # Create new trigger
            trigger = Trigger(
                user_id=trigger_data.user_id,
                workflow_id=trigger_data.workflow_id,
                trigger_type=trigger_data.trigger_type,
                trigger_name=trigger_data.trigger_name,
                trigger_config=trigger_data.trigger_config,
                event_types=trigger_data.event_types,
                is_active=True,
            )
            session.add(trigger)
            await session.flush()  # Get the ID

            # Create trigger configuration for adapter
            # Convert string event types to enum values
            enum_event_types = []
            for et in trigger_data.event_types:
                if et == "created":
                    enum_event_types.append(TriggerEventType.CREATED)
                elif et == "updated":
                    enum_event_types.append(TriggerEventType.UPDATED)
                elif et == "deleted":
                    enum_event_types.append(TriggerEventType.DELETED)
                elif et == "reminder":
                    enum_event_types.append(TriggerEventType.REMINDER)

            trigger_config_obj = TriggerConfiguration(
                trigger_id=trigger.id,
                user_id=trigger_data.user_id,
                workflow_id=trigger_data.workflow_id,
                event_types=enum_event_types,
                config=trigger_data.trigger_config,
                is_active=True,
            )

            # Register with adapter, passing the current session
            success = await adapter.register_trigger(trigger_config_obj, session)
            if success:
                logger.info("Successfully created trigger", trigger_id=trigger.id)
                return trigger
            else:
                logger.error(
                    f"Failed to register trigger with adapter {trigger_data.trigger_type}"
                )
                return None

        except IntegrityError as e:
            # Handle unique constraint violations
            error_msg = str(e.orig) if hasattr(e, "orig") else str(e)
            if "uq_user_workflow_trigger_name" in error_msg:
                logger.warning(
                    f"Trigger name already exists for user and workflow",
                    user_id=trigger_data.user_id,
                    workflow_id=trigger_data.workflow_id,
                    trigger_name=trigger_data.trigger_name,
                )
                # Return None to indicate constraint violation - API will handle the error message
                return None
            else:
                logger.error(f"Database integrity error creating trigger", error=str(e))
                return None
        except Exception as e:
            logger.error(f"Failed to create trigger", error=str(e))
            return None

    async def remove_trigger(self, trigger_id: UUID) -> bool:
        """
        Remove an existing trigger.

        Args:
            trigger_id: Unique identifier for the trigger to remove

        Returns:
            bool: True if trigger was removed successfully
        """
        try:
            async with self.db_manager.get_async_session(auto_commit=False) as session:
                # Get trigger from database
                trigger = await session.get(Trigger, trigger_id)
                if not trigger:
                    logger.warning(f"Trigger {trigger_id} not found in database")
                    return False

                # Get adapter
                adapter = self.get_adapter(trigger.trigger_type)
                if not adapter:
                    logger.error(f"Adapter not found: {trigger.trigger_type}")
                    return False

                # Unregister from adapter
                success = await adapter.unregister_trigger(trigger_id)
                if success:
                    # Remove from database
                    await session.delete(trigger)
                    await session.commit()
                    logger.info("Successfully removed trigger", trigger_id=trigger_id)
                    return True
                else:
                    logger.error(
                        f"Failed to unregister trigger {trigger_id} from adapter"
                    )
                    return False

        except Exception as e:
            logger.error(f"Failed to remove trigger {trigger_id}", error=str(e))
            return False

    async def update_trigger(
        self, trigger_id: UUID, update_data: "TriggerUpdate"
    ) -> Optional["Trigger"]:
        """
        Update an existing trigger.

        Args:
            trigger_id: ID of the trigger to update
            update_data: Updated trigger data

        Returns:
            Trigger: Updated trigger object if successful, None otherwise
        """
        try:
            async with self.db_manager.get_async_session(auto_commit=False) as session:
                # Get the existing trigger
                trigger = await session.get(Trigger, trigger_id)
                if not trigger:
                    logger.warning(f"Trigger {trigger_id} not found for update")
                    return None

                # Store original values for adapter updates
                original_is_active = trigger.is_active
                original_config = trigger.trigger_config.copy()
                original_event_types = trigger.event_types.copy()

                # Validate trigger name uniqueness if it's being updated
                if update_data.trigger_name is not None:
                    # Check if another trigger with the same name exists for this user/workflow
                    existing_trigger_query = select(Trigger).where(
                        and_(
                            Trigger.user_id == trigger.user_id,
                            Trigger.workflow_id == trigger.workflow_id,
                            Trigger.trigger_name == update_data.trigger_name,
                            Trigger.id != trigger_id,  # Exclude the current trigger
                        )
                    )
                    existing_trigger_result = await session.execute(
                        existing_trigger_query
                    )
                    existing_trigger = existing_trigger_result.scalar_one_or_none()

                    if existing_trigger:
                        logger.warning(
                            f"Trigger name '{update_data.trigger_name}' already exists for user and workflow",
                            user_id=trigger.user_id,
                            workflow_id=trigger.workflow_id,
                            trigger_name=update_data.trigger_name,
                            existing_trigger_id=existing_trigger.id,
                            current_trigger_id=trigger_id,
                        )
                        return None

                # Update fields that are provided
                updated_fields = []
                if update_data.trigger_name is not None:
                    trigger.trigger_name = update_data.trigger_name
                    updated_fields.append("trigger_name")

                if update_data.trigger_config is not None:
                    # Merge input_values into trigger_config if provided separately
                    final_config = update_data.trigger_config.copy()
                    if update_data.input_values is not None:
                        final_config["input_values"] = update_data.input_values
                    trigger.trigger_config = final_config
                    updated_fields.append("trigger_config")
                elif update_data.input_values is not None:
                    # Update only input_values in existing config
                    updated_config = trigger.trigger_config.copy()
                    updated_config["input_values"] = update_data.input_values
                    trigger.trigger_config = updated_config
                    updated_fields.append("input_values")

                if update_data.event_types is not None:
                    trigger.event_types = update_data.event_types
                    updated_fields.append("event_types")

                if update_data.is_active is not None:
                    trigger.is_active = update_data.is_active
                    updated_fields.append("is_active")

                # Update the updated_at timestamp
                trigger.updated_at = datetime.now()

                # Get the adapter for this trigger type
                adapter = self.get_adapter(trigger.trigger_type)
                if not adapter:
                    logger.error(
                        f"Adapter not found for trigger type: {trigger.trigger_type}"
                    )
                    return None

                # Handle adapter-specific updates
                adapter_update_needed = False

                # Check if configuration or event types changed
                if (
                    update_data.trigger_config is not None
                    and update_data.trigger_config != original_config
                ):
                    adapter_update_needed = True

                if (
                    update_data.event_types is not None
                    and update_data.event_types != original_event_types
                ):
                    adapter_update_needed = True

                # If adapter update is needed, we need to re-register the trigger
                if adapter_update_needed:
                    # Unregister the old trigger configuration
                    await adapter.unregister_trigger(trigger_id)

                    # Convert string event types to enum values for adapter
                    enum_event_types = []
                    for et in trigger.event_types:
                        if et == "created":
                            enum_event_types.append(TriggerEventType.CREATED)
                        elif et == "updated":
                            enum_event_types.append(TriggerEventType.UPDATED)
                        elif et == "deleted":
                            enum_event_types.append(TriggerEventType.DELETED)
                        elif et == "reminder":
                            enum_event_types.append(TriggerEventType.REMINDER)

                    # Create new trigger configuration
                    trigger_config_obj = TriggerConfiguration(
                        trigger_id=trigger.id,
                        user_id=trigger.user_id,
                        workflow_id=trigger.workflow_id,
                        event_types=enum_event_types,
                        config=trigger.trigger_config,
                        is_active=trigger.is_active,
                    )

                    # Re-register with new configuration
                    logger.info(
                        f"Re-registering trigger after configuration update",
                        trigger_id=trigger.id,
                        user_id=trigger.user_id,
                        workflow_id=trigger.workflow_id,
                        trigger_type=trigger.trigger_type,
                    )

                    success = await adapter.register_trigger(
                        trigger_config_obj, session
                    )
                    if not success:
                        logger.error(
                            f"Failed to re-register trigger {trigger_id} with updated configuration",
                            trigger_id=trigger.id,
                            user_id=trigger.user_id,
                            trigger_type=trigger.trigger_type,
                        )
                        return None

                    logger.info(
                        f"Successfully re-registered trigger after configuration update",
                        trigger_id=trigger.id,
                        user_id=trigger.user_id,
                        trigger_type=trigger.trigger_type,
                    )

                # Handle activation state changes
                elif (
                    update_data.is_active is not None
                    and update_data.is_active != original_is_active
                ):
                    if update_data.is_active:
                        await adapter.resume_trigger(trigger_id)
                    else:
                        await adapter.pause_trigger(trigger_id)

                # Commit the database changes
                await session.commit()
                await session.refresh(trigger)

                logger.info(
                    f"Successfully updated trigger {trigger_id}",
                    updated_fields=updated_fields,
                    adapter_update_needed=adapter_update_needed,
                )
                return trigger

        except IntegrityError as e:
            # Handle unique constraint violations
            error_msg = str(e.orig) if hasattr(e, "orig") else str(e)
            if "uq_user_workflow_trigger_name" in error_msg:
                logger.warning(
                    f"Trigger name already exists for user and workflow",
                    trigger_id=trigger_id,
                    trigger_name=(
                        update_data.trigger_name
                        if update_data.trigger_name
                        else "unknown"
                    ),
                )
                # Return None to indicate constraint violation - API will handle the error message
                return None
            else:
                logger.error(
                    f"Database integrity error updating trigger {trigger_id}",
                    error=str(e),
                )
                return None
        except Exception as e:
            logger.error(f"Failed to update trigger {trigger_id}", error=str(e))
            return None

    async def process_event(self, adapter_name: str, raw_event: Dict[str, Any]) -> bool:
        """
        Process an incoming event from an adapter and execute matching workflows.

        Args:
            adapter_name: Name of the adapter that received the event
            raw_event: Raw event data from the external service

        Returns:
            bool: True if event was processed successfully
        """
        try:
            adapter = self.get_adapter(adapter_name)
            if not adapter:
                logger.error(f"Adapter not found: {adapter_name}")
                return False

            # Process the raw event
            trigger_event = await adapter.process_event(raw_event)

            if not trigger_event:
                logger.debug("Event ignored by adapter", adapter_name=adapter_name)
                return True  # Not an error, just ignored

            # Find matching triggers
            matching_triggers = await self._find_matching_triggers(
                adapter_name, trigger_event
            )

            # Execute workflows for matching triggers
            for trigger in matching_triggers:
                await self._execute_trigger_workflow(trigger, trigger_event)

            logger.info(
                f"Processed event {trigger_event.event_id} from {adapter_name}, "
                f"matched {len(matching_triggers)} triggers"
            )
            return True

        except Exception as e:
            logger.error(f"Failed to process event from {adapter_name}", error=str(e))
            return False

    async def health_check(self) -> Dict[str, AdapterHealthStatus]:
        """
        Check health of all registered adapters.

        Returns:
            Dict[str, AdapterHealthStatus]: Health status for each adapter
        """
        health_status = {}
        for name, adapter in self._adapters.items():
            try:
                health_status[name] = await adapter.health_check()
            except Exception as e:
                health_status[name] = AdapterHealthStatus(
                    is_healthy=False, last_check=datetime.now(), error_message=str(e)
                )

        return health_status

    async def get_trigger_by_id(self, trigger_id: str) -> Optional[Trigger]:
        """
        Get a specific trigger by its ID.

        Args:
            trigger_id: ID of the trigger

        Returns:
            Optional[Trigger]: Trigger if found, None otherwise
        """
        try:
            async with self.db_manager.get_async_session() as session:
                result = await session.execute(
                    select(Trigger).where(Trigger.id == trigger_id)
                )
                return result.scalars().first()
        except Exception as e:
            logger.error(f"Failed to get trigger {trigger_id}", error=str(e))
            return None

    async def get_triggers_for_user(self, user_id: str) -> List[Trigger]:
        """
        Get all triggers for a specific user.

        Args:
            user_id: ID of the user

        Returns:
            List[Trigger]: List of user's triggers
        """
        try:
            async with self.db_manager.get_async_session() as session:
                result = await session.execute(
                    select(Trigger).where(Trigger.user_id == user_id)
                )
                return result.scalars().all()
        except Exception as e:
            logger.error(f"Failed to get triggers for user {user_id}", error=str(e))
            return []

    async def get_triggers_for_workflow(self, workflow_id: str) -> List[Trigger]:
        """
        Get all triggers for a specific workflow.

        Args:
            workflow_id: ID of the workflow

        Returns:
            List[Trigger]: List of workflow's triggers
        """
        try:
            async with self.db_manager.get_async_session() as session:
                result = await session.execute(
                    select(Trigger).where(Trigger.workflow_id == workflow_id)
                )
                return result.scalars().all()
        except Exception as e:
            logger.error(
                f"Failed to get triggers for workflow {workflow_id}", error=str(e)
            )
            return []

    async def list_triggers(
        self,
        user_id: Optional[str] = None,
        workflow_id: Optional[str] = None,
        trigger_type: Optional[str] = None,
        is_active: Optional[bool] = None,
        page: int = 1,
        page_size: int = 20,
    ) -> Tuple[List[Trigger], int]:
        """
        List triggers with optional filtering and pagination.

        Args:
            user_id: Optional user ID filter
            workflow_id: Optional workflow ID filter
            trigger_type: Optional trigger type filter
            is_active: Optional active status filter
            page: Page number (1-based)
            page_size: Number of items per page

        Returns:
            tuple[List[Trigger], int]: Tuple of (triggers, total_count)
        """
        try:
            async with self.db_manager.get_async_session() as session:
                # Build base query
                query = select(Trigger)
                count_query = select(func.count(Trigger.id))

                # Apply filters
                conditions = []
                if user_id:
                    conditions.append(Trigger.user_id == user_id)
                if workflow_id:
                    conditions.append(Trigger.workflow_id == workflow_id)
                if trigger_type:
                    conditions.append(Trigger.trigger_type == trigger_type)
                if is_active is not None:
                    conditions.append(Trigger.is_active == is_active)

                if conditions:
                    query = query.where(and_(*conditions))
                    count_query = count_query.where(and_(*conditions))

                # Get total count
                total_result = await session.execute(count_query)
                total_count = total_result.scalar()

                # Apply pagination and ordering
                query = query.order_by(desc(Trigger.created_at))
                query = query.offset((page - 1) * page_size).limit(page_size)

                # Execute query
                result = await session.execute(query)
                triggers = result.scalars().all()

                logger.debug(
                    f"Listed triggers: page={page}, page_size={page_size}, "
                    f"total={total_count}, returned={len(triggers)}, "
                    f"filters: user_id={user_id}, workflow_id={workflow_id}, "
                    f"trigger_type={trigger_type}, is_active={is_active}"
                )

                return triggers, total_count

        except Exception as e:
            logger.error(f"Failed to list triggers", error=str(e))
            return [], 0

    async def toggle_trigger(self, trigger_id: UUID, is_active: bool) -> bool:
        """
        Enable or disable a trigger.

        Args:
            trigger_id: ID of the trigger
            is_active: Whether to activate or deactivate

        Returns:
            bool: True if toggle was successful
        """
        try:
            async with self.db_manager.get_async_session(auto_commit=False) as session:
                trigger = await session.get(Trigger, trigger_id)
                if not trigger:
                    logger.warning(f"Trigger {trigger_id} not found")
                    return False

                trigger.is_active = is_active
                await session.commit()

                # Update adapter
                adapter = self.get_adapter(trigger.trigger_type)
                if adapter:
                    if is_active:
                        await adapter.resume_trigger(trigger_id)
                    else:
                        await adapter.pause_trigger(trigger_id)

                logger.info(
                    f"Trigger {trigger_id} {'activated' if is_active else 'deactivated'}"
                )
                return True

        except Exception as e:
            logger.error(f"Failed to toggle trigger {trigger_id}", error=str(e))
            return False

    async def _find_matching_triggers(
        self, adapter_name: str, trigger_event: TriggerEvent
    ) -> List[Trigger]:
        """
        Find triggers that match the given event.

        Args:
            adapter_name: Name of the adapter
            trigger_event: Event to match against

        Returns:
            List[Trigger]: List of matching triggers
        """
        try:
            async with self.db_manager.get_async_session() as session:
                # Build the query
                query = select(Trigger).where(
                    and_(
                        Trigger.trigger_type == adapter_name,
                        Trigger.is_active == True,
                        Trigger.event_types.contains([trigger_event.event_type]),
                    )
                )

                # Find active triggers for this adapter type that monitor this event type
                # Use eager loading to prevent N+1 queries when accessing executions
                query = query.options(selectinload(Trigger.executions))
                result = await session.execute(query)
                triggers = result.scalars().all()

                logger.debug(
                    f"Found {len(triggers)} matching triggers for {adapter_name}"
                )

                # Additional filtering based on trigger configuration could be added here
                # For now, return all matching triggers
                return triggers

        except Exception as e:
            logger.error(f"Failed to find matching triggers", error=str(e))
            return []

    async def _execute_trigger_workflow(
        self, trigger: Trigger, trigger_event: TriggerEvent
    ) -> None:
        """
        Execute workflow for a triggered event.

        Args:
            trigger: Trigger that was activated
            trigger_event: Event that activated the trigger
        """
        try:
            # Create execution record
            async with self.db_manager.get_async_session(auto_commit=False) as session:
                # Convert trigger_event to JSON-serializable format
                import json

                event_data_json = json.loads(trigger_event.model_dump_json())

                # Add trigger configuration to event data for workflow executor
                event_data_json["trigger_config"] = trigger.trigger_config

                execution = TriggerExecution(
                    trigger_id=trigger.id,
                    event_data=event_data_json,
                    status="pending",
                )
                session.add(execution)
                await session.flush()  # Get the ID

                logger.info(
                    f"Created trigger execution record {execution.id} for trigger {trigger.id}"
                )

                try:
                    # Execute workflow with fetch using the new method
                    correlation_id = (
                        await self._workflow_executor.execute_workflow_with_fetch(
                            db_session=session,
                            trigger_execution=execution,
                            user_id=trigger.user_id,
                            workflow_id=trigger.workflow_id,
                            event_data=event_data_json,
                        )
                    )

                    if correlation_id:
                        # Update trigger last_triggered_at
                        trigger.last_triggered_at = datetime.now()
                        execution.completed_at = datetime.now()

                        logger.info(
                            f"Successfully executed workflow for trigger {trigger.id}, "
                            f"correlation_id: {correlation_id}"
                        )
                    else:
                        execution.completed_at = datetime.now()
                        logger.error(
                            f"Workflow execution failed for trigger {trigger.id}"
                        )

                    # Commit the transaction
                    await session.commit()

                except Exception as e:
                    execution.status = "failed"
                    execution.error_message = str(e)
                    execution.completed_at = datetime.now()
                    await session.commit()
                    logger.error(
                        f"Failed to execute workflow for trigger {trigger.id}",
                        error=str(e),
                    )

        except Exception as e:
            logger.error(
                f"Failed to process trigger execution for trigger {trigger.id}",
                error=str(e),
            )

    async def get_execution_history(
        self, trigger_id: UUID, limit: int = 100
    ) -> List[TriggerExecution]:
        """
        Get execution history for a trigger.

        Args:
            trigger_id: ID of the trigger
            limit: Maximum number of executions to return

        Returns:
            List[TriggerExecution]: List of executions
        """
        try:
            async with self.db_manager.get_async_session() as session:
                result = await session.execute(
                    select(TriggerExecution)
                    .where(TriggerExecution.trigger_id == trigger_id)
                    .order_by(desc(TriggerExecution.executed_at))
                    .limit(limit)
                )
                return result.scalars().all()

        except Exception as e:
            logger.error(
                f"Failed to get execution history for trigger {trigger_id}",
                error=str(e),
            )
            return []

    async def get_adapter_statistics(self) -> Dict[str, Dict[str, Any]]:
        """
        Get statistics for all adapters.

        Returns:
            Dict[str, Dict[str, Any]]: Statistics for each adapter
        """
        stats = {}
        for name, adapter in self._adapters.items():
            try:
                health = await adapter.health_check()
                stats[name] = {
                    "is_healthy": health.is_healthy,
                    "last_check": health.last_check.isoformat(),
                    "active_triggers": adapter.get_trigger_count(),
                    "error_message": health.error_message,
                }
            except Exception as e:
                stats[name] = {
                    "is_healthy": False,
                    "last_check": datetime.now().isoformat(),
                    "active_triggers": 0,
                    "error_message": str(e),
                }

        return stats
