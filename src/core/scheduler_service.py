import asyncio
import os
from typing import Optional
from sqlalchemy.ext.asyncio import AsyncSession

from src.core.scheduler_engine import SchedulerEngine
from src.core.scheduler_manager import SchedulerManager
from src.core.instance_coordinator import InstanceCoordinator
from src.core.distributed_lock import (
    LockManager,
    RedisDistributedLock,
    DatabaseDistributedLock,
)
from src.core.task_queue import RedisTaskQueue, TaskWorker
from src.core.workflow_task_handler import WorkflowTask<PERSON>andler
from src.core.workflow_executor import WorkflowExecutor
from src.core.metrics import scheduler_metrics, task_queue_metrics
from src.database.connection import get_async_session
from src.utils.logger import get_logger
from src.utils.config import get_settings

logger = get_logger(__name__)


class SchedulerService:
    """
    Main service class that orchestrates the scheduler engine, task queue, and workers.
    This provides a unified interface for running the scalable scheduler system.
    """

    def __init__(
        self,
        redis_url: Optional[str] = None,
        max_concurrent_schedulers: int = 10,
        batch_size: int = 50,
        worker_concurrency: int = 5,
        enable_metrics: bool = True,
    ):
        self.redis_url = redis_url or os.getenv("REDIS_URL", "redis://localhost:6379")
        self.max_concurrent_schedulers = max_concurrent_schedulers
        self.batch_size = batch_size
        self.worker_concurrency = worker_concurrency
        self.enable_metrics = enable_metrics

        # Components
        self.db_session: Optional[AsyncSession] = None
        self.task_queue: Optional[RedisTaskQueue] = None
        self.lock_manager: Optional[LockManager] = None
        self.scheduler_engine: Optional[SchedulerEngine] = None
        self.task_worker: Optional[TaskWorker] = None
        self.workflow_executor: Optional[WorkflowExecutor] = None
        self.instance_coordinator: Optional[InstanceCoordinator] = None

        # Control
        self.running = False
        self.worker_task: Optional[asyncio.Task] = None

        logger.info("SchedulerService initialized")

    async def initialize(self):
        """Initialize all components."""
        logger.info("Initializing SchedulerService components...")

        try:
            # Initialize database session
            self.db_session = await get_async_session().__anext__()

            # Try to initialize Redis components with fallback
            redis_available = await self._check_redis_availability()

            if redis_available:
                logger.info("Redis is available, initializing full scalable mode")
                # Initialize task queue
                self.task_queue = RedisTaskQueue(self.redis_url)

                # Initialize distributed locking with Redis primary
                redis_lock = RedisDistributedLock(self.redis_url)
                db_lock = DatabaseDistributedLock(self.db_session)
                self.lock_manager = LockManager(
                    primary_lock=redis_lock, fallback_lock=db_lock
                )

                # Initialize workflow task handler
                workflow_handler = WorkflowTaskHandler(
                    workflow_executor=WorkflowExecutor()
                )

                # Initialize task worker
                self.task_worker = TaskWorker(
                    task_queue=self.task_queue, handlers=workflow_handler.get_handlers()
                )
            else:
                logger.warning(
                    "Redis is not available, falling back to database-only mode"
                )
                # Use database-only locking
                db_lock = DatabaseDistributedLock(self.db_session)
                self.lock_manager = LockManager(
                    primary_lock=db_lock, fallback_lock=None
                )

                # No task queue or workers in fallback mode
                self.task_queue = None
                self.task_worker = None

            # Initialize instance coordinator if Redis is available
            if redis_available:
                self.instance_coordinator = InstanceCoordinator(
                    lock_manager=self.lock_manager
                )

            # Initialize scheduler engine (works with or without task queue)
            self.scheduler_engine = SchedulerEngine(
                db_session=self.db_session,
                task_queue=self.task_queue,
                lock_manager=self.lock_manager,
                batch_size=self.batch_size,
                max_concurrency=self.max_concurrent_schedulers,
                instance_coordinator=self.instance_coordinator,
            )

            logger.info("SchedulerService components initialized successfully")

        except Exception as e:
            logger.error(f"Failed to initialize SchedulerService: {e}", exc_info=True)
            await self.cleanup()
            raise

    async def _check_redis_availability(self) -> bool:
        """Check if Redis is available and accessible."""
        try:
            import redis.asyncio as redis

            client = redis.from_url(self.redis_url)
            await client.ping()
            await client.close()
            return True
        except Exception as e:
            logger.debug(f"Redis not available: {e}")
            return False

    async def start(self):
        """Start the scheduler service."""
        if self.running:
            logger.warning("SchedulerService is already running")
            return

        if not self.scheduler_engine:
            await self.initialize()

        logger.info("Starting SchedulerService...")
        self.running = True

        try:
            # Start instance coordinator (if available)
            if self.instance_coordinator:
                await self.instance_coordinator.start()

            # Start task worker in background (if available)
            if self.task_worker:
                self.worker_task = asyncio.create_task(
                    self.task_worker.start(
                        queue_names=["workflow_execution"],
                        concurrency=self.worker_concurrency,
                    )
                )
                logger.info(
                    f"SchedulerService started with {self.worker_concurrency} workers "
                    f"processing workflow_execution queue"
                )
            else:
                logger.info(
                    "SchedulerService started in database-only mode (no Redis task queue)"
                )

        except Exception as e:
            logger.error(f"Failed to start SchedulerService: {e}", exc_info=True)
            await self.stop()
            raise

    async def stop(self):
        """Stop the scheduler service."""
        if not self.running:
            logger.warning("SchedulerService is not running")
            return

        logger.info("Stopping SchedulerService...")
        self.running = False

        try:
            # Stop task worker
            if self.task_worker:
                await self.task_worker.stop()

            # Cancel worker task
            if self.worker_task and not self.worker_task.done():
                self.worker_task.cancel()
                try:
                    await self.worker_task
                except asyncio.CancelledError:
                    pass

            await self.cleanup()
            logger.info("SchedulerService stopped successfully")

        except Exception as e:
            logger.error(f"Error stopping SchedulerService: {e}", exc_info=True)

    async def cleanup(self):
        """Clean up resources."""
        try:
            # Close database session
            if self.db_session:
                await self.db_session.close()
                self.db_session = None

            # Close Redis connections
            if self.task_queue and hasattr(self.task_queue, "redis_client"):
                await self.task_queue.redis_client.close()

            logger.debug("SchedulerService cleanup completed")

        except Exception as e:
            logger.error(f"Error during SchedulerService cleanup: {e}", exc_info=True)

    async def run_scheduler_cycle(self):
        """Run a single scheduler processing cycle."""
        if not self.scheduler_engine:
            raise RuntimeError("SchedulerService not initialized")

        # Only log debug message if we're in debug mode to reduce noise
        # The scheduler engine will log when there's actual work to do
        await self.scheduler_engine.process_due_schedulers()

    async def get_metrics(self) -> dict:
        """Get comprehensive metrics from all components."""
        if not self.enable_metrics:
            return {"metrics_disabled": True}

        metrics = {
            "service_status": {
                "running": self.running,
                "components_initialized": all(
                    [
                        self.scheduler_engine is not None,
                        self.task_worker is not None,
                        self.task_queue is not None,
                        self.lock_manager is not None,
                    ]
                ),
            },
            "scheduler_metrics": scheduler_metrics.get_summary(),
            "task_queue_metrics": task_queue_metrics.get_summary(),
        }

        # Add task queue specific metrics if available
        if self.task_queue and hasattr(self.task_queue, "metrics"):
            metrics["redis_task_queue_metrics"] = self.task_queue.metrics.get_summary()

        # Add worker metrics if available
        if self.task_worker and hasattr(self.task_worker, "metrics"):
            metrics["worker_metrics"] = self.task_worker.metrics.get_summary()

        return metrics

    async def health_check(self) -> dict:
        """Perform a health check of all components."""
        health = {
            "status": "healthy",
            "components": {},
            "timestamp": asyncio.get_event_loop().time(),
        }

        try:
            # Check database connection
            if self.db_session:
                await self.db_session.execute("SELECT 1")
                health["components"]["database"] = "healthy"
            else:
                health["components"]["database"] = "not_initialized"

            # Check Redis connection
            if self.task_queue and hasattr(self.task_queue, "redis_client"):
                await self.task_queue.redis_client.ping()
                health["components"]["redis"] = "healthy"
            else:
                health["components"]["redis"] = "not_initialized"

            # Check service status
            health["components"]["scheduler_service"] = (
                "running" if self.running else "stopped"
            )
            health["components"]["task_worker"] = (
                "running" if (self.task_worker and self.running) else "stopped"
            )

        except Exception as e:
            health["status"] = "unhealthy"
            health["error"] = str(e)
            logger.error(f"Health check failed: {e}", exc_info=True)

        return health


# Global service instance
_scheduler_service: Optional[SchedulerService] = None


async def get_scheduler_service() -> SchedulerService:
    """Get or create the global scheduler service instance."""
    global _scheduler_service

    if _scheduler_service is None:
        settings = get_settings()
        _scheduler_service = SchedulerService(
            redis_url=getattr(settings, "redis_url", "redis://localhost:6379"),
            max_concurrent_schedulers=getattr(
                settings, "max_concurrent_schedulers", 10
            ),
            batch_size=getattr(settings, "scheduler_batch_size", 50),
            worker_concurrency=getattr(settings, "worker_concurrency", 5),
        )
        await _scheduler_service.initialize()

    return _scheduler_service


async def start_scheduler_service():
    """Start the global scheduler service."""
    service = await get_scheduler_service()
    await service.start()


async def stop_scheduler_service():
    """Stop the global scheduler service."""
    global _scheduler_service
    if _scheduler_service:
        await _scheduler_service.stop()
        _scheduler_service = None
