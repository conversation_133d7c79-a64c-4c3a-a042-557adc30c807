"""
Instance Coordinator - Manages coordination between multiple scheduler instances.

This module provides functionality for:
1. Instance registration and discovery
2. Load balancing coordination
3. Health monitoring across instances
4. Graceful shutdown coordination
"""

import asyncio
import json
import time
import uuid
from datetime import datetime, timezone
from typing import Dict, List, Optional, Set
from dataclasses import dataclass, asdict

import structlog
from src.core.distributed_lock import LockManager
from src.utils.config import get_settings

logger = structlog.get_logger(__name__)


@dataclass
class InstanceInfo:
    """Information about a scheduler instance."""

    instance_id: str
    hostname: str
    port: int
    started_at: datetime
    last_heartbeat: datetime
    status: str  # 'active', 'draining', 'stopped'
    scheduler_concurrency: int
    worker_concurrency: int
    current_load: float  # 0.0 to 1.0
    version: str


class InstanceCoordinator:
    """
    Coordinates multiple scheduler instances for optimal load distribution.
    """

    def __init__(self, lock_manager: LockManager, instance_id: Optional[str] = None):
        self.lock_manager = lock_manager
        self.settings = get_settings()
        self.instance_id = (
            instance_id
            or getattr(self.settings, "instance_id", None)
            or f"scheduler-{uuid.uuid4().hex[:8]}"
        )
        self.hostname = getattr(self.settings, "hostname", "localhost")
        self.port = getattr(self.settings, "port", 8000)
        self.started_at = datetime.now(timezone.utc)
        self.heartbeat_interval = 30  # seconds
        self.instance_timeout = 90  # seconds
        self.running = False
        self.heartbeat_task: Optional[asyncio.Task] = None

    async def start(self):
        """Start the instance coordinator."""
        if self.running:
            logger.warning("Instance coordinator already running")
            return

        self.running = True
        logger.info(f"Starting instance coordinator for {self.instance_id}")

        # Register this instance
        await self._register_instance()

        # Start heartbeat task
        self.heartbeat_task = asyncio.create_task(self._heartbeat_loop())

        logger.info(f"Instance coordinator started for {self.instance_id}")

    async def stop(self):
        """Stop the instance coordinator and deregister instance."""
        if not self.running:
            return

        logger.info(f"Stopping instance coordinator for {self.instance_id}")
        self.running = False

        # Cancel heartbeat task
        if self.heartbeat_task:
            self.heartbeat_task.cancel()
            try:
                await self.heartbeat_task
            except asyncio.CancelledError:
                pass

        # Mark instance as stopped
        await self._update_instance_status("stopped")

        # Clean up after delay to allow other instances to see the status
        await asyncio.sleep(5)
        await self._deregister_instance()

        logger.info(f"Instance coordinator stopped for {self.instance_id}")

    async def get_active_instances(self) -> List[InstanceInfo]:
        """Get list of all active instances."""
        try:
            # Use distributed lock to ensure consistent reads
            lock_key = "instance_registry"
            if await self.lock_manager.acquire_scheduler_lock(lock_key, ttl=10):
                try:
                    instances = await self._get_all_instances()
                    # Filter out expired instances
                    now = datetime.now(timezone.utc)
                    active_instances = []

                    for instance in instances:
                        time_since_heartbeat = (
                            now - instance.last_heartbeat
                        ).total_seconds()
                        if (
                            time_since_heartbeat <= self.instance_timeout
                            and instance.status == "active"
                        ):
                            active_instances.append(instance)

                    return active_instances
                finally:
                    await self.lock_manager.release_scheduler_lock(lock_key)
            else:
                logger.warning("Could not acquire lock for instance registry read")
                return []

        except Exception as e:
            logger.error(f"Error getting active instances: {e}", exc_info=True)
            return []

    async def get_optimal_instance_for_scheduling(self) -> Optional[str]:
        """
        Get the instance ID that should handle the next batch of schedulers.
        Returns None if this instance should handle it.
        """
        try:
            active_instances = await self.get_active_instances()

            if len(active_instances) <= 1:
                # Only this instance or no coordination needed
                return None

            # Find instance with lowest load
            min_load = float("inf")
            optimal_instance = None

            for instance in active_instances:
                if instance.current_load < min_load:
                    min_load = instance.current_load
                    optimal_instance = instance.instance_id

            # If this instance has the lowest load, return None (handle locally)
            if optimal_instance == self.instance_id:
                return None

            return optimal_instance

        except Exception as e:
            logger.error(f"Error determining optimal instance: {e}", exc_info=True)
            return None

    async def update_load_metrics(self, current_load: float):
        """Update the current load metrics for this instance."""
        try:
            await self._update_instance_load(current_load)
        except Exception as e:
            logger.error(f"Error updating load metrics: {e}", exc_info=True)

    async def _register_instance(self):
        """Register this instance in the distributed registry."""
        instance_info = InstanceInfo(
            instance_id=self.instance_id,
            hostname=self.hostname,
            port=self.port,
            started_at=self.started_at,
            last_heartbeat=datetime.now(timezone.utc),
            status="active",
            scheduler_concurrency=getattr(self.settings, "scheduler_concurrency", 10),
            worker_concurrency=getattr(self.settings, "task_worker_concurrency", 5),
            current_load=0.0,
            version=getattr(self.settings, "version", "1.0.0"),
        )

        lock_key = f"instance:{self.instance_id}"
        if await self.lock_manager.acquire_scheduler_lock(lock_key, ttl=60):
            try:
                # Store instance info in Redis or database
                await self._store_instance_info(instance_info)
                logger.info(f"Registered instance {self.instance_id}")
            finally:
                await self.lock_manager.release_scheduler_lock(lock_key)

    async def _heartbeat_loop(self):
        """Periodic heartbeat to maintain instance registration."""
        while self.running:
            try:
                await self._send_heartbeat()
                await asyncio.sleep(self.heartbeat_interval)
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in heartbeat loop: {e}", exc_info=True)
                await asyncio.sleep(5)  # Brief pause before retry

    async def _send_heartbeat(self):
        """Send heartbeat to update last_heartbeat timestamp."""
        lock_key = f"instance:{self.instance_id}"
        if await self.lock_manager.acquire_scheduler_lock(lock_key, ttl=10):
            try:
                await self._update_instance_heartbeat()
            finally:
                await self.lock_manager.release_scheduler_lock(lock_key)

    async def _store_instance_info(self, instance_info: InstanceInfo):
        """Store instance information (implement based on your storage backend)."""
        # This would typically store in Redis or database
        # For now, we'll use Redis if available
        if hasattr(self.lock_manager.primary_lock, "redis_client"):
            redis_client = self.lock_manager.primary_lock.redis_client
            key = f"instance_registry:{self.instance_id}"
            data = json.dumps(asdict(instance_info), default=str)
            await redis_client.setex(key, self.instance_timeout + 30, data)

    async def _update_instance_heartbeat(self):
        """Update the heartbeat timestamp for this instance."""
        if hasattr(self.lock_manager.primary_lock, "redis_client"):
            redis_client = self.lock_manager.primary_lock.redis_client
            key = f"instance_registry:{self.instance_id}"

            # Get current data and update heartbeat
            data = await redis_client.get(key)
            if data:
                instance_data = json.loads(data)
                instance_data["last_heartbeat"] = datetime.now(timezone.utc).isoformat()
                updated_data = json.dumps(instance_data)
                await redis_client.setex(key, self.instance_timeout + 30, updated_data)

    async def _update_instance_status(self, status: str):
        """Update the status of this instance."""
        if hasattr(self.lock_manager.primary_lock, "redis_client"):
            redis_client = self.lock_manager.primary_lock.redis_client
            key = f"instance_registry:{self.instance_id}"

            data = await redis_client.get(key)
            if data:
                instance_data = json.loads(data)
                instance_data["status"] = status
                instance_data["last_heartbeat"] = datetime.now(timezone.utc).isoformat()
                updated_data = json.dumps(instance_data)
                await redis_client.setex(key, self.instance_timeout + 30, updated_data)

    async def _update_instance_load(self, current_load: float):
        """Update the current load for this instance."""
        if hasattr(self.lock_manager.primary_lock, "redis_client"):
            redis_client = self.lock_manager.primary_lock.redis_client
            key = f"instance_registry:{self.instance_id}"

            data = await redis_client.get(key)
            if data:
                instance_data = json.loads(data)
                instance_data["current_load"] = current_load
                instance_data["last_heartbeat"] = datetime.now(timezone.utc).isoformat()
                updated_data = json.dumps(instance_data)
                await redis_client.setex(key, self.instance_timeout + 30, updated_data)

    async def _get_all_instances(self) -> List[InstanceInfo]:
        """Get all registered instances."""
        instances = []

        if hasattr(self.lock_manager.primary_lock, "redis_client"):
            redis_client = self.lock_manager.primary_lock.redis_client
            keys = await redis_client.keys("instance_registry:*")

            for key in keys:
                data = await redis_client.get(key)
                if data:
                    try:
                        instance_data = json.loads(data)
                        # Convert datetime strings back to datetime objects
                        instance_data["started_at"] = datetime.fromisoformat(
                            instance_data["started_at"]
                        )
                        instance_data["last_heartbeat"] = datetime.fromisoformat(
                            instance_data["last_heartbeat"]
                        )
                        instances.append(InstanceInfo(**instance_data))
                    except Exception as e:
                        logger.warning(f"Error parsing instance data for {key}: {e}")

        return instances

    async def _deregister_instance(self):
        """Remove this instance from the registry."""
        if hasattr(self.lock_manager.primary_lock, "redis_client"):
            redis_client = self.lock_manager.primary_lock.redis_client
            key = f"instance_registry:{self.instance_id}"
            await redis_client.delete(key)
            logger.info(f"Deregistered instance {self.instance_id}")
