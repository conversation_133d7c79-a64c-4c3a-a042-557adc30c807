"""
Prometheus Metrics Integration for Workflow Automation Platform.

Provides comprehensive metrics collection for:
- Scheduler performance
- Task queue metrics
- Database performance
- Instance coordination
- HTTP request metrics
"""

import time
from typing import Dict, Any, Optional
from functools import wraps

from prometheus_client import (
    Counter,
    Histogram,
    Gauge,
    Info,
    CollectorRegistry,
    generate_latest,
    CONTENT_TYPE_LATEST
)
from fastapi import Response
import structlog

logger = structlog.get_logger(__name__)

# Create custom registry for better control
REGISTRY = CollectorRegistry()

# Scheduler Metrics
scheduler_cycles_total = Counter(
    'scheduler_cycles_total',
    'Total number of scheduler cycles executed',
    ['status'],
    registry=REGISTRY
)

scheduler_processing_duration = Histogram(
    'scheduler_processing_duration_seconds',
    'Time spent processing schedulers',
    ['mode'],
    buckets=[0.1, 0.5, 1.0, 2.0, 5.0, 10.0, 30.0, 60.0],
    registry=REGISTRY
)

schedulers_processed_total = Counter(
    'schedulers_processed_total',
    'Total number of schedulers processed',
    ['status'],
    registry=REGISTRY
)

schedulers_queued_total = Counter(
    'schedulers_queued_total',
    'Total number of schedulers queued for execution',
    registry=REGISTRY
)

scheduler_lock_acquisitions = Counter(
    'scheduler_lock_acquisitions_total',
    'Total number of scheduler lock acquisitions',
    ['status'],
    registry=REGISTRY
)

scheduler_concurrent_processing = Gauge(
    'scheduler_concurrent_processing_current',
    'Current number of schedulers being processed concurrently',
    registry=REGISTRY
)

# Task Queue Metrics
task_queue_size = Gauge(
    'task_queue_size_current',
    'Current size of task queues',
    ['queue_name'],
    registry=REGISTRY
)

tasks_processed_total = Counter(
    'tasks_processed_total',
    'Total number of tasks processed',
    ['task_type', 'status'],
    registry=REGISTRY
)

task_processing_duration = Histogram(
    'task_processing_duration_seconds',
    'Time spent processing tasks',
    ['task_type'],
    buckets=[0.1, 0.5, 1.0, 2.0, 5.0, 10.0, 30.0],
    registry=REGISTRY
)

task_retries_total = Counter(
    'task_retries_total',
    'Total number of task retries',
    ['task_type'],
    registry=REGISTRY
)

# Database Metrics
database_connections_active = Gauge(
    'database_connections_active_current',
    'Current number of active database connections',
    registry=REGISTRY
)

database_query_duration = Histogram(
    'database_query_duration_seconds',
    'Database query execution time',
    ['operation'],
    buckets=[0.001, 0.005, 0.01, 0.05, 0.1, 0.5, 1.0, 2.0],
    registry=REGISTRY
)

database_errors_total = Counter(
    'database_errors_total',
    'Total number of database errors',
    ['error_type'],
    registry=REGISTRY
)

# HTTP Request Metrics
http_requests_total = Counter(
    'http_requests_total',
    'Total number of HTTP requests',
    ['method', 'endpoint', 'status_code'],
    registry=REGISTRY
)

http_request_duration = Histogram(
    'http_request_duration_seconds',
    'HTTP request duration',
    ['method', 'endpoint'],
    buckets=[0.01, 0.05, 0.1, 0.5, 1.0, 2.0, 5.0],
    registry=REGISTRY
)

# Instance Coordination Metrics
active_instances = Gauge(
    'active_instances_current',
    'Current number of active instances',
    registry=REGISTRY
)

instance_load_score = Gauge(
    'instance_load_score_current',
    'Current load score of this instance',
    ['instance_id'],
    registry=REGISTRY
)

instance_coordination_errors = Counter(
    'instance_coordination_errors_total',
    'Total number of instance coordination errors',
    ['error_type'],
    registry=REGISTRY
)

# Workflow Execution Metrics
workflow_executions_total = Counter(
    'workflow_executions_total',
    'Total number of workflow executions',
    ['status'],
    registry=REGISTRY
)

workflow_execution_duration = Histogram(
    'workflow_execution_duration_seconds',
    'Workflow execution duration',
    buckets=[0.5, 1.0, 2.0, 5.0, 10.0, 30.0, 60.0, 120.0],
    registry=REGISTRY
)

# System Info
system_info = Info(
    'system_info',
    'System information',
    registry=REGISTRY
)

# Redis Metrics
redis_operations_total = Counter(
    'redis_operations_total',
    'Total number of Redis operations',
    ['operation', 'status'],
    registry=REGISTRY
)

redis_connection_errors = Counter(
    'redis_connection_errors_total',
    'Total number of Redis connection errors',
    registry=REGISTRY
)


class MetricsCollector:
    """Centralized metrics collector for the application."""
    
    def __init__(self, instance_id: Optional[str] = None):
        self.instance_id = instance_id or "unknown"
        self._start_time = time.time()
        
        # Set system info
        system_info.info({
            'instance_id': self.instance_id,
            'version': '1.0.0',  # Should come from config
            'start_time': str(int(self._start_time))
        })
    
    def record_scheduler_cycle(self, status: str, duration: float, mode: str = "unknown"):
        """Record scheduler cycle metrics."""
        scheduler_cycles_total.labels(status=status).inc()
        scheduler_processing_duration.labels(mode=mode).observe(duration)
    
    def record_scheduler_processed(self, status: str, count: int = 1):
        """Record scheduler processing metrics."""
        schedulers_processed_total.labels(status=status).inc(count)
    
    def record_scheduler_queued(self, count: int = 1):
        """Record scheduler queuing metrics."""
        schedulers_queued_total.inc(count)
    
    def record_scheduler_lock(self, status: str):
        """Record scheduler lock acquisition."""
        scheduler_lock_acquisitions.labels(status=status).inc()
    
    def set_concurrent_schedulers(self, count: int):
        """Set current concurrent scheduler count."""
        scheduler_concurrent_processing.set(count)
    
    def record_task_processed(self, task_type: str, status: str, duration: float):
        """Record task processing metrics."""
        tasks_processed_total.labels(task_type=task_type, status=status).inc()
        task_processing_duration.labels(task_type=task_type).observe(duration)
    
    def record_task_retry(self, task_type: str):
        """Record task retry."""
        task_retries_total.labels(task_type=task_type).inc()
    
    def set_task_queue_size(self, queue_name: str, size: int):
        """Set current task queue size."""
        task_queue_size.labels(queue_name=queue_name).set(size)
    
    def record_database_query(self, operation: str, duration: float):
        """Record database query metrics."""
        database_query_duration.labels(operation=operation).observe(duration)
    
    def record_database_error(self, error_type: str):
        """Record database error."""
        database_errors_total.labels(error_type=error_type).inc()
    
    def set_database_connections(self, count: int):
        """Set current database connection count."""
        database_connections_active.set(count)
    
    def record_http_request(self, method: str, endpoint: str, status_code: int, duration: float):
        """Record HTTP request metrics."""
        http_requests_total.labels(
            method=method, 
            endpoint=endpoint, 
            status_code=str(status_code)
        ).inc()
        http_request_duration.labels(method=method, endpoint=endpoint).observe(duration)
    
    def set_active_instances(self, count: int):
        """Set current active instance count."""
        active_instances.set(count)
    
    def set_instance_load(self, load_score: float):
        """Set current instance load score."""
        instance_load_score.labels(instance_id=self.instance_id).set(load_score)
    
    def record_coordination_error(self, error_type: str):
        """Record instance coordination error."""
        instance_coordination_errors.labels(error_type=error_type).inc()
    
    def record_workflow_execution(self, status: str, duration: Optional[float] = None):
        """Record workflow execution metrics."""
        workflow_executions_total.labels(status=status).inc()
        if duration is not None:
            workflow_execution_duration.observe(duration)
    
    def record_redis_operation(self, operation: str, status: str):
        """Record Redis operation metrics."""
        redis_operations_total.labels(operation=operation, status=status).inc()
    
    def record_redis_error(self):
        """Record Redis connection error."""
        redis_connection_errors.inc()


# Global metrics collector instance
_metrics_collector: Optional[MetricsCollector] = None


def get_metrics_collector() -> MetricsCollector:
    """Get the global metrics collector instance."""
    global _metrics_collector
    if _metrics_collector is None:
        _metrics_collector = MetricsCollector()
    return _metrics_collector


def init_metrics_collector(instance_id: Optional[str] = None):
    """Initialize the global metrics collector."""
    global _metrics_collector
    _metrics_collector = MetricsCollector(instance_id)


def metrics_endpoint() -> Response:
    """Generate Prometheus metrics endpoint response."""
    try:
        metrics_data = generate_latest(REGISTRY)
        return Response(
            content=metrics_data,
            media_type=CONTENT_TYPE_LATEST
        )
    except Exception as e:
        logger.error(f"Error generating metrics: {e}")
        return Response(
            content="# Error generating metrics\n",
            media_type=CONTENT_TYPE_LATEST,
            status_code=500
        )


def track_time(metric_name: str, labels: Optional[Dict[str, str]] = None):
    """Decorator to track execution time of functions."""
    def decorator(func):
        @wraps(func)
        async def async_wrapper(*args, **kwargs):
            start_time = time.time()
            try:
                result = await func(*args, **kwargs)
                duration = time.time() - start_time
                
                # Record success metric
                if metric_name == 'scheduler_processing':
                    get_metrics_collector().record_scheduler_cycle('success', duration)
                elif metric_name == 'database_query':
                    operation = labels.get('operation', 'unknown') if labels else 'unknown'
                    get_metrics_collector().record_database_query(operation, duration)
                
                return result
            except Exception as e:
                duration = time.time() - start_time
                
                # Record error metric
                if metric_name == 'scheduler_processing':
                    get_metrics_collector().record_scheduler_cycle('error', duration)
                elif metric_name == 'database_query':
                    get_metrics_collector().record_database_error(type(e).__name__)
                
                raise
        
        @wraps(func)
        def sync_wrapper(*args, **kwargs):
            start_time = time.time()
            try:
                result = func(*args, **kwargs)
                duration = time.time() - start_time
                
                # Record success metric based on metric_name
                return result
            except Exception as e:
                duration = time.time() - start_time
                # Record error metric
                raise
        
        return async_wrapper if hasattr(func, '__code__') and func.__code__.co_flags & 0x80 else sync_wrapper
    
    return decorator
