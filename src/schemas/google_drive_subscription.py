"""
Simple Google Drive subscription schema.
"""

from typing import List, Optional
from pydantic import BaseModel


class GoogleDriveSubscriptionCreate(BaseModel):
    """Schema for creating a Google Drive subscription."""

    organization_id: str
    event_types: List[str]


class GoogleDriveSubscriptionResponse(BaseModel):
    """Schema for Google Drive subscription response."""

    id: str
    organization_id: str
    event_types: List[str]
    channel_id: str
    resource_id: str
    is_active: bool
    start_page_token: Optional[str] = None
    current_page_token: Optional[str] = None
    expires_at: str
    created_at: str
    updated_at: str
