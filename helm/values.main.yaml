autoscaling:
  enabled: true
  minReplicas: 1
  maxReplicas: 1
  targetCPUUtilizationPercentage: 70
  # targetMemoryUtilizationPercentage: 70

resources:
  requests:
    memory: "250Mi"
    cpu: "100m"
  limits:
    memory: "600Mi"
    cpu: "200m"

ingress:
  enabled: true
  className: nginx
  annotations:
    nginx.ingress.kubernetes.io/rewrite-target: /
  hosts:
    - host: trigger-api.ruh.ai
      paths:
        - path: /
          pathType: Prefix
  tls: []
